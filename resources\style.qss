/* ECN成本管理系统样式表 */

/* 全局样式 */
QMainWindow, QDialog {
    background-color: #f5f5f5;
}

QWidget {
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
    font-size: 12px;
}

/* 标题样式 */
QLabel#titleLabel {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    padding: 5px;
}

/* 按钮样式 */
QPushButton {
    background-color: #2c7dfa;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #1a6eeb;
}

QPushButton:pressed {
    background-color: #1560d6;
}

QPushButton:disabled {
    background-color: #b5b5b5;
    color: #e0e0e0;
}

/* 删除按钮样式 */
QPushButton#deleteButton {
    background-color: #f44336;
}

QPushButton#deleteButton:hover {
    background-color: #e53935;
}

QPushButton#deleteButton:pressed {
    background-color: #d32f2f;
}

/* 表格样式 */
QTableView {
    border: 1px solid #d0d0d0;
    gridline-color: #e0e0e0;
    selection-background-color: #e6f2ff;
    selection-color: #000000;
    alternate-background-color: #fafafa;
    background-color: white;
}

QTableView::item {
    padding: 4px;
}

QTableView::item:selected {
    background-color: #e6f2ff;
    color: #000000;
}

QTableView QHeaderView::section {
    background-color: #f0f0f0;
    padding: 5px;
    border: 1px solid #d0d0d0;
    border-left: 0px;
    border-top: 0px;
    font-weight: bold;
    color: #333333;
}

/* 分页控件样式 */
QPushButton#pageButton {
    min-width: 30px;
    padding: 3px;
}

QLabel#pageInfoLabel {
    color: #555555;
}

/* 文本框样式 */
QLineEdit {
    border: 1px solid #d0d0d0;
    border-radius: 3px;
    padding: 4px;
    background-color: white;
    selection-background-color: #2c7dfa;
}

QLineEdit:focus {
    border: 1px solid #2c7dfa;
}

/* 总价显示控件样式 */
QLabel#totalPriceLabel {
    font-size: 14px;
    font-weight: bold;
    color: #2c7dfa;
    padding: 5px;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #f0f0f0;
    color: #555555;
}

/* Tab 页样式 */
QTabWidget::pane {
    border: 1px solid #d0d0d0;
    background-color: white;
    padding: 5px;
}

QTabBar::tab {
    background-color: #f0f0f0;
    border: 1px solid #d0d0d0;
    border-bottom: none;
    padding: 8px 15px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom-color: white;
}

QTabBar::tab:hover:!selected {
    background-color: #e6f2ff;
} 