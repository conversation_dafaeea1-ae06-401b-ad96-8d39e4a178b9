#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
变更单管理页面模块，实现添加/删除变更单文件功能
"""

import os
from PyQt6.QtCore import Qt, pyqtSignal, QThreadPool
from PyQt6.QtWidgets import (
    QWidget, QTableWidget, QTableWidgetItem, QPushButton, QLabel,
    QVBoxLayout, QHBoxLayout, QFileDialog, QMessageBox, QHeaderView,
    QProgressDialog, QTextEdit, QLineEdit, QAbstractItemView, QListView,
    QTreeView, QDialog, QListWidget, QListWidgetItem, QDialogButtonBox
)
import config
from utils.logger import info, debug, error
from utils.file_manager import file_manager
from models.data_manager import data_manager
from .background_tasks import Worker
from PyQt6.QtGui import QIcon
from PyQt6.QtWidgets import QApplication
import traceback

class ECNManagementPage(QWidget):
    """ECN变更单管理页面，用于添加/删除变更单文件"""
    
    dataChanged = pyqtSignal()  # 信号：数据变更
    
    def __init__(self, parent=None):
        """
        初始化ECN变更单管理页面
        
        Args:
            parent: 父部件
        """
        super().__init__(parent)
        self._init_ui()
        self._setup_connections()
        
        # 初始化线程池
        self.thread_pool = QThreadPool()
        info(f"ECN管理页面初始化线程池，最大线程数: {self.thread_pool.maxThreadCount()}")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题标签
        title_label = QLabel("ECN变更单管理")
        title_label.setObjectName("titleLabel")
        main_layout.addWidget(title_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 添加按钮
        self.add_button = QPushButton("添加变更单")
        self.add_button.clicked.connect(self._add_document)
        button_layout.addWidget(self.add_button)
        
        # 添加文件夹按钮
        self.add_folder_button = QPushButton("添加文件夹")
        self.add_folder_button.setIcon(QIcon(":/icons/add.png"))
        self.add_folder_button.clicked.connect(self._add_documents_from_folders)
        button_layout.addWidget(self.add_folder_button)
        
        # 删除按钮
        self.delete_button = QPushButton("删除选中")
        self.delete_button.setObjectName("deleteButton")
        self.delete_button.clicked.connect(self._on_delete_files)
        self.delete_button.setEnabled(False)  # 初始时禁用
        button_layout.addWidget(self.delete_button)
        
        button_layout.addStretch(1)
        main_layout.addLayout(button_layout)
        
        # 表格控件
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(5)
        self._column_names = ["文件名", "ERP号", "设备名称", "项目编号", "变更单号"]
        self.table_widget.setHorizontalHeaderLabels(self._column_names)
        self.table_widget.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table_widget.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)
        self.table_widget.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setShowGrid(True)
        self.table_widget.setStyleSheet("gridline-color: #d0d0d0;")
        
        # 设置表头
        header = self.table_widget.horizontalHeader()
        header.setStretchLastSection(False)  # 禁止最后一列自动拉伸
        
        # 设置合理的初始列宽
        self._set_initial_column_widths()
        
        # 表格选中行变化
        self.table_widget.itemSelectionChanged.connect(self._on_selection_changed)
        
        main_layout.addWidget(self.table_widget)
        
        # 状态标签
        self.status_label = QLabel("当前没有变更单文件")
        main_layout.addWidget(self.status_label)
        
        self.setLayout(main_layout)
    
    def _set_initial_column_widths(self):
        """设置表格的初始列宽"""
        try:
            # 设置各列的宽度
            column_widths = {
                "文件名": 300,
                "ERP号": 120,
                "设备名称": 180,
                "项目编号": 150,
                "变更单号": 200
            }
            
            # 应用设置的列宽
            for col, name in enumerate(self._column_names):
                if name in column_widths:
                    self.table_widget.setColumnWidth(col, column_widths[name])
                else:
                    # 对于未指定宽度的列，设置一个默认宽度
                    self.table_widget.setColumnWidth(col, 120)
            
            debug("已设置变更单管理表格初始列宽")
        except Exception as e:
            error(f"设置初始列宽时出错: {str(e)}")
    
    def _setup_connections(self):
        """设置信号连接"""
        # 暂无其他连接
        pass
    
    def _add_document(self):
        """添加单个或多个变更单文档"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择一个或多个文件", "", "Excel文件 (*.xlsx *.xls *.xlsm)"
        )
        if file_paths:
            self._run_add_files_task(file_paths)
    
    # 此方法已经被_run_add_files_task替代

    def _on_add_files_finished(self, results):
        """
        后台文件解析和添加任务完成后的处理
        
        Args:
            results: 后台任务返回的结果列表，每个元素是一个元组 (file_path, success, message)
        """
        self.progress_dialog.close()
        
        successful_adds = []
        failed_adds = []
        
        # 分类处理成功和失败的记录
        for file_path, success, message in results:
            if success:
                successful_adds.append(os.path.basename(file_path))
            else:
                failed_adds.append((os.path.basename(file_path), message))

        # 根据结果显示不同的信息
        total_files = len(results)
        success_count = len(successful_adds)
        failure_count = len(failed_adds)

        # 构建最终的提示信息
        if failure_count == 0:
            summary_message = f"成功添加 {success_count} 个变更单文件。"
            detailed_text = ""
        else:
            summary_message = f"文件添加完成：\n\n- 成功: {success_count} 个\n- 失败: {failure_count} 个"
            
            # 创建详细的失败原因列表
            failure_details = "\n\n失败详情:\n" + "="*20
            for name, reason in failed_adds:
                failure_details += f"\n- 文件: {name}\n  原因: {reason}\n"
            
            detailed_text = failure_details

        # 创建并显示消息框
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setWindowTitle("导入结果")
        msg_box.setText(summary_message)
        if detailed_text:
            msg_box.setDetailedText(detailed_text)
            # 查找并调整详细信息文本框的大小
            text_edit = msg_box.findChild(QTextEdit)
            if text_edit:
                text_edit.setMinimumSize(600, 250)
                # 禁用自动换行以更好地显示长文件名
                text_edit.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)

        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.exec()

        # 如果有任何成功添加，则刷新表格和数据
        if success_count > 0:
            self._refresh_table()
            self.dataChanged.emit()

    def _on_add_files_error(self, error_message):
        """处理后台添加文件时的通用错误"""
        if self.progress_dialog.isVisible():
            self.progress_dialog.close()
        QMessageBox.critical(self, "处理失败", f"添加文件时发生意外错误:\n{error_message}")
    
    def _on_delete_files(self):
        """删除选中文件按钮点击事件"""
        try:
            # 获取选中的行索引
            selected_rows = set()
            for item in self.table_widget.selectedItems():
                selected_rows.add(item.row())
            
            if not selected_rows:
                return
            
            # 获取所有文档信息
            documents = data_manager.get_all_documents()
            
            # 要删除的文件路径
            files_to_remove = []
            
            # 根据选中的行索引获取对应的文件路径
            for row in sorted(selected_rows):
                if 0 <= row < len(documents):
                    files_to_remove.append(documents[row]["file_path"])
            
            # 确认删除
            if files_to_remove:
                reply = QMessageBox.question(
                    self,
                    "确认删除",
                    f"确定要删除选中的 {len(files_to_remove)} 个文件吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                
                if reply == QMessageBox.StandardButton.Yes:
                    # 记录成功删除的文件数
                    removed_count = 0
                    
                    # 逐个删除文件
                    for file_path in files_to_remove:
                        if data_manager.remove_ecn_document(file_path):
                            removed_count += 1
                    
                    # 刷新表格显示
                    self._refresh_table()
                    
                    # 更新状态标签
                    self.status_label.setText(f"已删除 {removed_count} 个文件")
                    
                    # 发送数据变更信号
                    self.dataChanged.emit()
                    
                    info(f"删除变更单文件：成功 {removed_count} 个")
            
        except Exception as e:
            error(f"删除文件时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"删除文件时出错: {str(e)}")
    
    def _on_selection_changed(self):
        """表格选择变化事件"""
        # 更新删除按钮状态
        has_selection = len(self.table_widget.selectedItems()) > 0
        self.delete_button.setEnabled(has_selection)
    
    def _refresh_table(self):
        """刷新表格内容"""
        try:
            # 获取所有文档信息
            documents = data_manager.get_all_documents()
            
            # 清空表格
            self.table_widget.setRowCount(0)
            
            if not documents:
                self.status_label.setText("当前没有变更单文件")
                return
            
            # 添加行
            for row, doc in enumerate(documents):
                self.table_widget.insertRow(row)
                
                # 文件名
                self.table_widget.setItem(row, 0, QTableWidgetItem(doc["file_name"]))
                # ERP号
                self.table_widget.setItem(row, 1, QTableWidgetItem(doc["erp_number"]))
                # 设备名称
                self.table_widget.setItem(row, 2, QTableWidgetItem(doc["device_name"]))
                # 项目编号
                self.table_widget.setItem(row, 3, QTableWidgetItem(doc["project_number"]))
                # 变更单号
                self.table_widget.setItem(row, 4, QTableWidgetItem(doc["ecn_number"]))
            
            # 更新状态标签
            self.status_label.setText(f"共 {len(documents)} 个变更单文件")
            
            # 确保列宽合理
            self._set_initial_column_widths()
            
        except Exception as e:
            error(f"刷新表格时出错: {str(e)}")
    
    def refresh(self):
        """供外部调用的刷新方法"""
        self._refresh_table()

    def _add_documents_from_folders(self):
        """从选择的文件夹中批量添加Excel文档，支持多选文件夹"""
        class MultiFolderSelectionDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setWindowTitle("选择多个文件夹")
                self.resize(600, 400)
                
                # 创建主布局
                layout = QVBoxLayout(self)
                
                # 顶部说明标签
                tip_label = QLabel("请逐个选择要添加的文件夹，可以添加多个文件夹")
                layout.addWidget(tip_label)
                
                # 按钮区域
                button_layout = QHBoxLayout()
                self.add_folder_button = QPushButton("添加文件夹")
                self.add_folder_button.clicked.connect(self._add_folder)
                button_layout.addWidget(self.add_folder_button)
                
                self.remove_folder_button = QPushButton("移除选中文件夹")
                self.remove_folder_button.clicked.connect(self._remove_selected_folder)
                self.remove_folder_button.setEnabled(False)
                button_layout.addWidget(self.remove_folder_button)
                
                button_layout.addStretch()
                layout.addLayout(button_layout)
                
                # 已选择文件夹列表
                list_layout = QVBoxLayout()
                list_label = QLabel("已选择的文件夹:")
                list_layout.addWidget(list_label)
                
                self.folders_list = QListWidget()
                self.folders_list.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
                self.folders_list.itemSelectionChanged.connect(self._on_selection_changed)
                list_layout.addWidget(self.folders_list)
                
                layout.addLayout(list_layout)
                
                # 确认和取消按钮
                dialog_buttons = QDialogButtonBox(
                    QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
                )
                dialog_buttons.accepted.connect(self.accept)
                dialog_buttons.rejected.connect(self.reject)
                layout.addWidget(dialog_buttons)
                
                # 存储已选择的文件夹路径
                self.selected_folders = []
            
            def _add_folder(self):
                """添加文件夹到选择列表"""
                folder = QFileDialog.getExistingDirectory(self, "选择一个文件夹", "")
                if folder:
                    # 检查是否已经添加过这个文件夹
                    if folder not in self.selected_folders:
                        self.selected_folders.append(folder)
                        item = QListWidgetItem(folder)
                        self.folders_list.addItem(item)
                    else:
                        QMessageBox.information(self, "提示", "此文件夹已添加过")
            
            def _remove_selected_folder(self):
                """从选择列表中移除选中的文件夹"""
                current_row = self.folders_list.currentRow()
                if current_row >= 0:
                    removed_folder = self.selected_folders.pop(current_row)
                    self.folders_list.takeItem(current_row)
                    info(f"移除了文件夹: {removed_folder}")
            
            def _on_selection_changed(self):
                """处理列表选择变化"""
                self.remove_folder_button.setEnabled(self.folders_list.currentRow() >= 0)
                
            def getSelectedFolders(self):
                """获取所有选择的文件夹"""
                return self.selected_folders
        
        # 创建并显示多文件夹选择对话框
        dialog = MultiFolderSelectionDialog(self)
        if dialog.exec() != QDialog.DialogCode.Accepted:
            return
            
        # 获取所有选择的文件夹
        selected_folders = dialog.getSelectedFolders()
        
        if not selected_folders:
            QMessageBox.information(self, "选择结果", "未选择任何文件夹。")
            return
            
        # 处理所有选中的文件夹
        info(f"选择了 {len(selected_folders)} 个文件夹")
        
        all_excel_files = []
        excel_extensions = ('.xlsx', '.xls', '.xlsm')
        
        # 从所有选中的文件夹中收集Excel文件
        for folder in selected_folders:
            info(f"处理文件夹: {folder}")
            # 递归查找所有Excel文件
            for root, _, files in os.walk(folder):
                for file in files:
                    if file.lower().endswith(excel_extensions):
                        all_excel_files.append(os.path.join(root, file))
        
        if all_excel_files:
            # 检查并排除重复文件名的文件
            unique_files = self._filter_duplicate_filenames(all_excel_files)
            
            info(f"总共找到 {len(all_excel_files)} 个Excel文件，去重后剩余 {len(unique_files)} 个")
            
            # 调用run_add_files_task处理文件
            self._run_add_files_task(unique_files)
        else:
            QMessageBox.information(self, "导入结果", "在选择的文件夹中未找到Excel文件。")
            
    def _filter_duplicate_filenames(self, file_paths):
        """
        过滤掉具有相同文件名的文件，保留每个文件名只出现一次
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            已去除重复文件名的文件路径列表
        """
        # 检查数据管理器中已有的文件名
        existing_documents = data_manager.get_all_documents()
        existing_filenames = set(doc["file_name"] for doc in existing_documents)
        
        # 用于检测重复的字典：文件名 -> 路径
        filename_dict = {}
        duplicate_count = 0
        
        # 先检查与已有文件的重复
        for file_path in file_paths:
            file_name = os.path.basename(file_path)
            if file_name in existing_filenames:
                duplicate_count += 1
                debug(f"文件 '{file_name}' 已存在于数据库中，跳过")
                continue
            
            # 再检查当前批次内的重复
            if file_name in filename_dict:
                duplicate_count += 1
                debug(f"发现重复文件名: '{file_name}', 保留 '{filename_dict[file_name]}', 跳过 '{file_path}'")
            else:
                filename_dict[file_name] = file_path
        
        # 如果有重复文件名，显示提示
        if duplicate_count > 0:
            info(f"跳过了 {duplicate_count} 个重复文件名的文件")
            QMessageBox.information(
                self, 
                "文件去重提示", 
                f"已自动跳过 {duplicate_count} 个文件名重复的文件，以避免重复导入。"
            )
        
        return list(filename_dict.values())

    def _run_add_files_task(self, file_paths):
        """
        运行添加文件的后台任务
        
        Args:
            file_paths: 文件路径列表
        """
        if not file_paths:
            return
            
        # 初始化进度对话框
        self.progress_dialog = QProgressDialog("正在添加和解析文件...", "取消", 0, len(file_paths), self)
        self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        self.progress_dialog.setWindowTitle("处理中")
        self.progress_dialog.setAutoClose(False) # 解析完成后手动关闭
        self.progress_dialog.show()

        # 创建后台工作线程
        worker = Worker(data_manager.parse_files_in_background, file_paths)
        
        # 连接信号
        worker.signals.progress.connect(self.progress_dialog.setValue)
        worker.signals.finished.connect(self._on_add_files_finished)
        worker.signals.error.connect(self._on_add_files_error)
        
        # 当用户点击取消时，尝试停止线程池中的任务
        self.progress_dialog.canceled.connect(self.thread_pool.clear)

        # 开始执行
        self.thread_pool.start(worker)

    # 此方法已被_on_delete_files替代