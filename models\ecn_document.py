#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
变更单文档类模块，表示单个ECN变更单文件及其元数据
"""

import os
from typing import Dict, List, Any, Optional
from utils.logger import info, error, debug
from utils.excel_parser import excel_parser

class ECNDocument:
    """变更单文档类，表示单个ECN变更单文件"""
    
    def __init__(self, file_path: str):
        """
        初始化变更单文档
        
        Args:
            file_path: 变更单文件路径
        """
        self.file_path = file_path
        self.file_name = os.path.basename(file_path)
        self.basic_info: Dict[str, str] = {}
        self.items: List[Dict[str, Any]] = []
        self.is_parsed = False
        
        # 解析文件
        self._parse_document()
    
    def _parse_document(self):
        """解析变更单文档"""
        try:
            info(f"开始解析变更单文档: {self.file_name}")
            
            # 使用Excel解析器解析文件
            basic_info, items = excel_parser.parse_excel(self.file_path)
            
            if not basic_info:
                error(f"无法从文件中提取基本信息: {self.file_path}")
                return
            
            if not items:
                error(f"无法从文件中提取变更单条目: {self.file_path}")
                return
            
            # 保存解析结果
            self.basic_info = basic_info
            self.items = items
            self.is_parsed = True
            
            info(f"变更单文档解析成功: {self.file_name}, 包含 {len(items)} 个条目")
            
        except Exception as e:
            error(f"解析变更单文档时出错: {self.file_path}, 错误: {str(e)}")
    
    def get_erp_number(self) -> str:
        """获取ERP号"""
        return self.basic_info.get("ERP号", "")
    
    def get_device_name(self) -> str:
        """获取设备名称"""
        return self.basic_info.get("设备名称", "")
    
    def get_project_number(self) -> str:
        """获取项目编号"""
        return self.basic_info.get("项目编号", "")
    
    def get_project_name(self) -> str:
        """获取项目名称"""
        return self.basic_info.get("项目名称", "")
    
    def get_ecn_number(self) -> str:
        """获取变更单号"""
        return self.basic_info.get("变更单号", "")
    
    def get_items_count(self) -> int:
        """获取变更单条目数量"""
        return len(self.items)
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取变更单摘要信息
        
        Returns:
            包含变更单基本信息和条目数量的字典
        """
        return {
            "file_name": self.file_name,
            "file_path": self.file_path,
            "erp_number": self.get_erp_number(),
            "device_name": self.get_device_name(),
            "project_number": self.get_project_number(),
            "project_name": self.get_project_name(),
            "ecn_number": self.get_ecn_number(),
            "items_count": self.get_items_count(),
            "is_parsed": self.is_parsed
        }
    
    def get_content_items(self) -> List[Dict[str, Any]]:
        """
        获取转换为内容页面格式的条目列表
        
        Returns:
            转换后的内容条目列表
        """
        if not self.is_parsed:
            error(f"文档未成功解析，无法获取内容条目: {self.file_path}")
            return []
        
        content_items = []
        
        try:
            for item in self.items:
                # 从变更单号解析工号和变更日期
                ecn_number = self.get_ecn_number()
                parts = ecn_number.split('-')
                job_number = ""
                change_date = ""
                if len(parts) >= 3:
                    job_number = parts[1]
                    change_date = parts[2]

                content_item = {
                    "零件名称": item.get("名称", ""),
                    "零件编码": item.get("零件编码", ""),
                    "ERP号": self.get_erp_number(),
                    "计划号": item.get("涉及计划号", ""),
                    "所属设备名称": self.get_device_name(),
                    "所属项目名称": self.get_project_name(),
                    "所属项目编号": self.get_project_number(),
                    "所属变更单单号": self.get_ecn_number(),
                    "新版本实施内容": item.get("新版本实施内容", ""),
                    "变更原因": item.get("变更原因", ""),
                    "问题分类": item.get("问题分类", ""),
                    "工号": job_number,
                    "变更日期": change_date,
                    "数量": item.get("总数量", "0"),
                    "单价": "0.00",  # 单价默认为0
                    "金额": "0.00",   # 金额默认为0
                    # 添加"已有零件处理建议"四列数据
                    "已有零件处理建议-在制在购": item.get("已有零件处理建议-在制在购", ""),
                    "已有零件处理建议-库存": item.get("已有零件处理建议-库存", ""),
                    "已有零件处理建议-使用中": item.get("已有零件处理建议-使用中", ""),
                    #"已有零件处理建议-变更成本": item.get("已有零件处理建议-变更成本", "")
                }
                
                # 添加调试日志，确认"已有零件处理建议"数据是否被正确添加
                if item.get("已有零件处理建议-使用中", ""):
                    debug(f"零件 {content_item['零件编码']} 的'已有零件处理建议-使用中'值: '{item.get('已有零件处理建议-使用中', '')}'")
                
                # 尝试将数量转换为数值
                try:
                    quantity = float(content_item["数量"])
                    content_item["数量"] = str(quantity)
                except (ValueError, TypeError):
                    content_item["数量"] = "0"
                
                content_items.append(content_item)
        
        except Exception as e:
            error(f"转换内容条目时出错: {str(e)}")
        
        return content_items 