/* ui/styles.qss */

/* 通用窗口样式 */
QDialog, QMainWindow { /* QDialog 和 QMainWindow 的样式 */
    background-color: #f8faff; /* 淡蓝灰色背景颜色 */
    color: #333; /* 深灰色文本颜色 */
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif; /* 优先使用现代字体 */
}

/* 标签样式 */
QLabel { /* QLabel 的默认样式 */
    font-size: 14px; /* 字体大小 */
    padding: 2px; /* 文本周围的内边距 */
    color: #444; /* 文本颜色 */
}

QLabel#contentLabel { /* 对象名为 "contentLabel" 的 QLabel 样式（用于登录窗口标题） */
    font-size: 20px; /* 更大的字体大小 */
    font-weight: bold; /* 粗体字重 */
    color: #1E88E5; /* 主题蓝色 */
    padding-bottom: 10px; /* 底部内边距 */
}

QLabel[class="windowTitle"] { /* 类属性为 "windowTitle" 的 QLabel 样式（用于自定义标题栏标题） */
    font-size: 15px; /* 字体大小 */
    font-weight: bold; /* 粗体字重 */
    color: #555; /* 文本颜色 */
    padding-left: 5px; /* 左侧内边距 */
}

/* 输入框样式 */
QLineEdit { /* QLineEdit 的默认样式 */
    border: 1px solid #ccc; /* 1像素实线浅灰色边框 */
    padding: 8px 10px; /* 输入框内部内边距 */
    border-radius: 5px; /* 圆角 */
    font-size: 14px; /* 字体大小 */
    background-color: #ffffff; /* 白色背景 */
    min-height: 15pxpx; /* 最小高度 */
    selection-background-color: #BBDEFB; /* 文本被选中时的浅蓝色背景 */
    selection-color: #333; /* 文本被选中时的深灰色文本颜色 */
}

QLineEdit:focus { /* QLineEdit 获得焦点时的样式 */
    border: 1px solid #1E88E5; /* 使用主题蓝色边框高亮焦点 */
}

QLineEdit[echoMode="2"] { /* echoMode 属性设置为 "2" 的 QLineEdit 样式（密码框） */
    /* 如果需要，可以添加密码框的特定样式 */
}

/* 按钮样式 */
QPushButton { /* QPushButton 的默认样式 */
    border: 1px solid #ccc; /* 1像素实线浅灰色边框 */
    padding: 6px 20px; /* 按钮内部内边距 */
    border-radius: 5px; /* 圆角 */
    font-size: 14px; /* 字体大小 */
    background-color: #f0f0f0; /* 浅灰色背景 */
    color: #333; /* 深灰色文本颜色 */
    min-height: 23px; /* 最小高度 */
    outline: none; /* 移除焦点时的虚线轮廓 */
}

QPushButton:hover { /* QPushButton 悬停时的样式 */
    background-color: #e0e0e0; /* 悬停时背景颜色略深 */
    border-color: #bbb; /* 悬停时边框颜色略深 */
}

QPushButton:pressed { /* QPushButton 被按下时的样式 */
    background-color: #d0d0d0; /* 按下时背景颜色更深 */
}

QPushButton:disabled { /* QPushButton 禁用时的样式 */
    background-color: #e8e8e8; /* 更浅的灰色背景 */
    color: #aaa; /* 浅灰色文本颜色 */
    border-color: #ddd; /* 浅灰色边框 */
}

/* 主要按钮样式 */
QPushButton[class="primary"] { /* 类属性为 "primary" 的 QPushButton 样式 */
    background-color: #1E88E5; /* 主题蓝色背景 */
    color: white; /* 白色文本颜色 */
    border-color: #1976D2; /* 更深的蓝色边框颜色 */
    font-weight: bold; /* 粗体字重 */
}

QPushButton[class="primary"]:hover { /* 主要按钮悬停时的样式 */
    background-color: #1976D2; /* 悬停时背景颜色更深 */
}

QPushButton[class="primary"]:pressed { /* 主要按钮被按下时的样式 */
    background-color: #1565C0; /* 按下时背景颜色更深 */
}

/* 危险操作按钮样式（例如：重置） */
QPushButton[class="destructive"] { /* 类属性为 "destructive" 的 QPushButton 样式 */
    background-color: #e53935; /* 红色背景 */
    color: white; /* 白色文本颜色 */
    border-color: #d32f2f; /* 更深的红色边框 */
    font-weight: bold; /* 粗体字重 */
}
QPushButton[class="destructive"]:hover { /* 危险操作按钮悬停时的样式 */
    background-color: #d32f2f; /* 悬停时背景颜色更深 */
}
QPushButton[class="destructive"]:pressed { /* 危险操作按钮被按下时的样式 */
    background-color: #c62828; /* 按下时背景颜色更深 */
}

/* 链接按钮样式 */
QPushButton[class="link"] { /* 类属性为 "link" 的 QPushButton 样式 */
    background-color: transparent; /* 透明背景 */
    color: #1976D2; /* 主题蓝色文本颜色 */
    border: none; /* 无边框 */
    text-decoration: underline; /* 下划线文本 */
    padding: 5px; /* 内边距 */
    font-size: 13px; /* 字体大小 */
    min-height: auto; /* 覆盖默认最小高度 */
}

QPushButton[class="link"]:hover { /* 链接按钮悬停时的样式 */
    color: #1565C0; /* 更深的蓝色文本颜色 */
    background-color: transparent; /* 确保悬停时无背景 */
    border: none; /* 确保悬停时无边框 */
}
QPushButton[class="link"]:pressed { /* 链接按钮被按下时的样式 */
    color: #0D47A1; /* 更深的蓝色文本颜色 */
    background-color: transparent; /* 确保按下时无背景 */
    border: none; /* 确保按下时无边框 */
}


/* 表格控件样式 */
QTableWidget { /* QTableWidget 的样式 */
    border: 1px solid #d0d0d0; /* 浅灰色边框 */
    gridline-color: #e0e0e0; /* 更浅的网格线颜色 */
    background-color: #ffffff; /* 白色背景 */
    alternate-background-color: #f7f7f7; /* 微妙的交替行背景颜色 */
    selection-background-color: #BBDEFB; /* 浅蓝色选中背景颜色 */
    selection-color: #333; /* 选中项的文本颜色 */
    font-size: 13px; /* 字体大小 */
}

QHeaderView::section { /* QHeaderView 的节（表头）样式 */
    background-color: #e8eaf6; /* 表头的浅靛蓝色背景 */
    color: #3f51b5; /* 靛蓝色文本颜色 */
    padding: 6px; /* 表头节的内边距 */
    border: none; /* 移除默认边框 */
    border-bottom: 1px solid #c5cae9; /* 底部边框用于分隔 */
    font-weight: bold; /* 粗体字重 */
    font-size: 13px; /* 字体大小 */
    text-align: left; /* 默认将表头文本左对齐 */
}

QHeaderView::section:horizontal { /* 横向表头节的样式 */
     padding-left: 10px; /* 为横向表头增加内边距 */
}

QHeaderView::section:vertical { /* 纵向表头节的样式 */
     /* 如果需要，可以添加纵向表头的特定样式 */
}

QTableWidget::item { /* QTableWidget 中项（单元格）的样式 */
    padding: 8px; /* 单元格内部更多的内边距 */
    border-bottom: 1px solid #eee; /* 行之间淡淡的线条 */
}

QTableWidget::item:selected { /* QTableWidget 中选中项的样式 */
    background-color: #BBDEFB; /* 选中时浅蓝色背景 */
    color: #333; /* 选中时深灰色文本颜色 */
}

/* 分组框/容器样式 */
QWidget[class="group-box"] { /* 类属性为 "group-box" 的 QWidget 样式 */
    border: 1px solid #e0e0e0; /* 浅灰色边框 */
    border-radius: 8px; /* 圆角 */
    background-color: #ffffff; /* 白色背景用于对比 */
    margin-top: 15px; /* 分组框上方增加空间 */
}

QLabel[class="group-box-title"] { /* 类属性为 "group-box-title" 的 QLabel 样式 */
    font-size: 15px; /* 字体大小 */
    font-weight: bold; /* 粗体字重 */
    color: #1E88E5; /* 主题蓝色标题颜色 */
    padding-bottom: 8px; /* 底部内边距 */
    qproperty-alignment: 'AlignCenter'; /* 使用 Qt 属性居中文本 */
}


/* 自定义标题栏按钮样式 */
#customTitleBar { /* 对象名为 "customTitleBar" 的控件样式 */
    background-color: #e8eaf6; /* 匹配表头背景颜色 */
}

#titleLabel { /* 对象名为 "titleLabel" 的标签样式 */
    color: #3f51b5; /* 匹配表头文本颜色 */
    font-weight: bold; /* 粗体字重 */
}

#customTitleBar QPushButton { /* 对象名为 "customTitleBar" 的控件内部的 QPushButton 样式 */
    background-color: transparent; /* 透明背景 */
    border: none; /* 无边框 */
    color: #5c6bc0; /* 略浅的靛蓝色文本颜色 */
    font-size: 16px; /* 调整符号大小 */
    padding: 0; /* 无内边距 */
    min-height: auto; /* 覆盖默认值 */
    min-width: 46px; /* 确保维持宽度 */
}

#customTitleBar QPushButton:hover { /* 标题栏按钮悬停时的样式 */
    background-color: rgba(92, 107, 192, 0.1); /* 带透明度的浅靛蓝色悬停背景 */
    color: #3f51b5; /* 悬停时靛蓝色文本颜色 */
}

#customTitleBar QPushButton:pressed { /* 标题栏按钮被按下时的样式 */
    background-color: rgba(92, 107, 192, 0.2); /* 带透明度的略深悬停背景 */
}

#customTitleBar QPushButton#closeBtn:hover { /* 标题栏关闭按钮悬停时的样式 */
    background-color: #e57373; /* 关闭按钮悬停时浅红色背景 */
    color: white; /* 悬停时白色文本颜色 */
}
#customTitleBar QPushButton#closeBtn:pressed { /* 标题栏关闭按钮被按下时的样式 */
    background-color: #ef5350; /* 关闭按钮按下时更深的红色背景 */
    color: white; /* 按下时白色文本颜色 */
}

/* QMessageBox 样式（基本） */
QMessageBox { /* QMessageBox 的基本样式 */
    background-color: #ffffff; /* 白色背景 */
}
QMessageBox QLabel { /* QMessageBox 内部 QLabel 的样式 */
    color: #333; /* 深灰色文本颜色 */
    font-size: 14px; /* 字体大小 */
}
QMessageBox QPushButton { /* QMessageBox 内部 QPushButton 的样式 */
    min-width: 80px; /* 确保按钮不会太小 */
}

/* 滚动条样式 */
QScrollBar:vertical { /* 纵向 QScrollBar 的样式 */
    border: none; /* 无边框 */
    background: #f0f0f0; /* 浅灰色背景 */
    width: 10px; /* 滚动条宽度 */
    margin: 0px 0 0px 0; /* 滚动条周围外边距 */
}
QScrollBar::handle:vertical { /* 纵向滚动条的滑块（thumb）样式 */
    background: #bdbdbd; /* 滑块的中灰色背景 */
    min-height: 20px; /* 滑块最小高度 */
    border-radius: 5px; /* 滑块圆角 */
}
QScrollBar::handle:vertical:hover { /* 滑块悬停时的样式 */
    background: #a0a0a0; /* 悬停时更深的灰色背景 */
}
QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical { /* 增加/减少行按钮（通常是箭头）样式 */
    height: 0px; /* 隐藏按钮 */
    background: none; /* 无背景 */
}
QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical { /* 滑块上方/下方区域的样式 */
    background: none; /* 无背景 */
}

QScrollBar:horizontal { /* 横向 QScrollBar 的样式 */
    border: none; /* 无边框 */
    background: #f0f0f0; /* 浅灰色背景 */
    height: 10px; /* 滚动条高度 */
    margin: 0px 0px 0px 0px; /* 滚动条周围外边距 */
}
QScrollBar::handle:horizontal { /* 横向滚动条的滑块（thumb）样式 */
    background: #bdbdbd; /* 滑块的中灰色背景 */
    min-width: 20px; /* 滑块最小宽度 */
    border-radius: 5px; /* 滑块圆角 */
}
QScrollBar::handle:horizontal:hover { /* 滑块悬停时的样式 */
    background: #a0a0a0; /* 悬停时更深的灰色背景 */
}
QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal { /* 增加/减少行按钮（通常是箭头）样式 */
    width: 0px; /* 隐藏按钮 */
    background: none; /* 无背景 */
}
QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal { /* 滑块左侧/右侧区域的样式 */
    background: none; /* 无背景 */
}

/* QProgressBar 用于忙碌指示器 */
QProgressBar { /* QProgressBar 的样式 */
    border: none; /* 移除边框 */
    background-color: transparent; /* 使背景透明 */
    text-align: center; /* 如果显示文本则居中 */
    height: 5px; /* 匹配现有高度 */
    border-radius: 2px; /* 圆角 */
}

QProgressBar::chunk { /* 进度块的样式 */
    background-color: #1E88E5; /* 使用主题蓝色作为进度条颜色 */
    border-radius: 2px;
     /* 不确定动画通常需要 C++ 或特定的 Qt 版本 */
}

/* QSplitter 句柄 */
QSplitter::handle { /* QSplitter 句柄的样式 */
    background-color: #e0e0e0; /* 浅灰色句柄背景 */
}

QSplitter::handle:vertical { /* 纵向 QSplitter 句柄的样式 */
    height: 5px; /* 句柄高度 */
}

QSplitter::handle:horizontal { /* 横向 QSplitter 句柄的样式 */
    width: 5px; /* 句柄宽度 */
}

QSplitter::handle:hover { /* QSplitter 句柄悬停时的样式 */
    background-color: #bdbdbd; /* 悬停时更深的灰色背景 */
}

/* Styles.qss - Qt StyleSheet for ECN Collection Mini-program */

/* General Window Styling */
QMainWindow {
    background-color: #f0f0f0; /* Light grey background */
}

QLabel {
    font-size: 10pt;
    color: #333333; /* Dark grey text */
}

QLineEdit {
    padding: 5px;
    border: 1px solid #cccccc;
    border-radius: 4px;
    font-size: 10pt;
    background-color: #ffffff; /* White background */
}

QPushButton {
    background-color: #0078d7; /* Blue background */
    color: white;
    border-radius: 4px;
    padding: 8px 15px;
    font-size: 10pt;
    border: 1px solid #005a9e; /* Darker blue border */
}

QPushButton:hover {
    background-color: #005a9e; /* Darker blue on hover */
}

QPushButton:pressed {
    background-color: #004c87; /* Even darker blue when pressed */
}

QTextEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    font-family: Consolas, Courier New, monospace; /* Monospaced font for logs */
    font-size: 9pt;
    background-color: #fdfdfd;
}

/* Style for QFileDialog if needed, though often system-styled */
QFileDialog {
    font-size: 10pt;
}

/* Specific object names can be used for more targeted styling if needed in gui.py */
/* For example:
QPushButton#mySpecificButton {
    background-color: #ff0000;
}
*/ 