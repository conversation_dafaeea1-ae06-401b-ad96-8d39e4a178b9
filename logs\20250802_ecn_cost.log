2025-08-02 13:47:58,042 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 13:47:58 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 15:28:31,559 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 15:28:31 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 15:29:00,224 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 15:29:00 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 15:29:01 - root - INFO - 数据管理器初始化完成
2025-08-02 15:29:01 - root - INFO - Application starting...
2025-08-02 15:29:02 - root - INFO - 成功加载样式表。
2025-08-02 15:29:03 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 15:29:03 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 15:29:03 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 15:29:03 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 15:29:03 - root - DEBUG - 已设置表格初始列宽
2025-08-02 15:29:03 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 15:29:03 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 15:29:03 - root - INFO - 主窗口初始化完成
2025-08-02 15:29:03 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 15:29:08 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 15:29:09 - root - DEBUG - 切换到标签页: 变更单管理
2025-08-02 16:01:40,021 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:01:40 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:01:40 - root - INFO - 数据管理器初始化完成
2025-08-02 16:01:40 - root - INFO - Application starting...
2025-08-02 16:01:40 - root - INFO - 成功加载样式表。
2025-08-02 16:01:41 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:01:41 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:01:41 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:01:41 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:01:41 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:01:41 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:01:41 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:01:41 - root - INFO - 主窗口初始化完成
2025-08-02 16:01:41 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:02:00 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:02:00 - root - DEBUG - 切换到标签页: 变更单管理
2025-08-02 16:02:03 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:02:04 - root - DEBUG - 切换到标签页: 变更单管理
2025-08-02 16:02:04 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:02:42 - root - DEBUG - 切换到标签页: 变更单管理
2025-08-02 16:02:48 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:02:48 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:02:48 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:02:48 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:02:48 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:02:48 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:02:48 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:02:48 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:02:48 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:02:48 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:02:48 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:02:48 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:02:48 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:02:48 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:02:48 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:02:48 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:02:48 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:02:48 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:02:48 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:02:48 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:02:48 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:02:48 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:02:48 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:02:49 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:02:49 - root - DEBUG - 数据变更事件触发
2025-08-02 16:02:50 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:02:52 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:02:52 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:02:52 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:02:52 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:02:52 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:02:52 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:02:52 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:02:52 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:02:52 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:02:52 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:02:52 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:02:52 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:02:52 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:02:52 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': ''}
2025-08-02 16:02:52 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:02:52 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:02:52 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:02:52 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:02:52 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:02:52 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:02:59 - root - DEBUG - 表头鼠标点击: 索引=8, 位置=(1087, 19)
2025-08-02 16:03:31 - root - DEBUG - DatabaseManager instance created.
2025-08-02 16:03:31 - root - INFO - SQL Server 数据库连接成功
2025-08-02 16:03:31 - root - DEBUG - 后台任务开始: fetch_prices_in_background
2025-08-02 16:03:31 - root - DEBUG - DatabaseManager instance created.
2025-08-02 16:03:31 - root - INFO - SQL Server 数据库连接成功
2025-08-02 16:03:31 - root - INFO - SQL Server 数据库连接成功
2025-08-02 16:03:31 - root - DEBUG - 正在从物料表 XC_OrdinaryMaterials 查询零件 '20065-04-356' 的单价
2025-08-02 16:03:31 - root - DEBUG - 查询到零件 '20065-04-356' 的单价为: 1200.00
2025-08-02 16:03:31 - root - INFO - 数据库连接已关闭
2025-08-02 16:03:31 - root - DEBUG - 计算金额: 1.0 * 1200.00 = 1200.00
2025-08-02 16:03:31 - root - INFO - 单价更新完成，共更新 1 个零件的单价
2025-08-02 16:03:31 - root - DEBUG - 后台任务完成: fetch_prices_in_background
2025-08-02 16:03:31 - root - INFO - 用户触发的单价更新完成，共更新 1 条记录
2025-08-02 16:03:31 - root - DEBUG - 开始筛选，条件: {}
2025-08-02 16:03:31 - root - DEBUG - 没有筛选条件，返回所有条目
2025-08-02 16:03:31 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:03:31 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:03:31 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:03:31 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:03:31 - root - DEBUG - self._checked_indices类型: <class 'set'>
2025-08-02 16:03:31 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:03:31 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:03:31 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:03:31 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:03:31 - root - DEBUG - 处理项目金额: key='金额', value_str='1200.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '1200.00', '金额': '1200.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': ''}
2025-08-02 16:03:31 - root - INFO - 计算总价: 1 个项目, 总额 = 1200.00
2025-08-02 16:03:31 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 1200.00
2025-08-02 16:03:31 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:03:31 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:03:31 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:03:38 - root - DEBUG - 表头鼠标点击: 索引=7, 位置=(794, 38)
2025-08-02 16:04:03 - root - INFO - 应用程序正常退出
2025-08-02 16:04:31,528 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:04:31 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:04:32 - root - INFO - 数据管理器初始化完成
2025-08-02 16:04:32 - root - INFO - Application starting...
2025-08-02 16:04:32 - root - INFO - 成功加载样式表。
2025-08-02 16:04:32 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:04:32 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:04:32 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:04:32 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:04:32 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:04:32 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:04:32 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:04:32 - root - INFO - 主窗口初始化完成
2025-08-02 16:04:32 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:04:44 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:04:45 - root - DEBUG - 切换到标签页: 变更单管理
2025-08-02 16:04:49 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:04:49 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:04:49 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:04:49 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:04:49 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:04:49 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:04:49 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:04:49 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:04:49 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:04:49 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:04:49 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:04:49 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:04:49 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:04:49 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:04:49 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:04:49 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:04:49 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:04:49 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:04:49 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:04:49 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:04:49 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:04:49 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:04:49 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:04:49 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:04:49 - root - DEBUG - 数据变更事件触发
2025-08-02 16:04:50 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:04:52 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:04:52 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:04:52 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:04:52 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:04:52 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:04:52 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:04:52 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:04:52 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:04:52 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:04:52 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:04:52 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:04:52 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:04:52 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:04:52 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': ''}
2025-08-02 16:04:52 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:04:52 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:04:52 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:04:52 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:04:52 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:04:52 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:07:32 - root - INFO - 应用程序正常退出
2025-08-02 16:07:49,702 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:07:49 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:07:50 - root - INFO - 数据管理器初始化完成
2025-08-02 16:07:50 - root - INFO - Application starting...
2025-08-02 16:07:50 - root - INFO - 成功加载样式表。
2025-08-02 16:07:50 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:07:50 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:07:50 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:07:50 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:07:50 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:07:50 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:07:50 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:07:50 - root - INFO - 主窗口初始化完成
2025-08-02 16:07:50 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:08:07 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:08:07 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:08:07 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:08:07 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:08:07 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:08:07 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:08:07 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:08:07 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:08:07 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:08:07 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:08:07 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:08:07 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:08:07 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:08:07 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:08:07 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:08:07 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:08:07 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:08:07 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:08:07 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:08:07 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:08:07 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:08:07 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:08:07 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:08:08 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:08:08 - root - DEBUG - 数据变更事件触发
2025-08-02 16:08:09 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:08:10 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:08:10 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:08:10 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:08:10 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:08:10 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:08:10 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:08:10 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:08:10 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:08:10 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:08:10 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:08:10 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:08:10 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:08:10 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:08:10 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': ''}
2025-08-02 16:08:10 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:08:10 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:08:10 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:08:10 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:08:10 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:08:10 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:08:20 - root - DEBUG - 切换到标签页: 变更单管理
2025-08-02 16:08:20 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:12:36 - root - INFO - 应用程序正常退出
2025-08-02 16:15:25,049 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:15:25 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:15:25 - root - INFO - 数据管理器初始化完成
2025-08-02 16:15:25 - root - INFO - Application starting...
2025-08-02 16:15:25 - root - INFO - 成功加载样式表。
2025-08-02 16:15:25 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:15:25 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:15:25 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:15:25 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:15:25 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:15:25 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:15:25 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:15:25 - root - INFO - 主窗口初始化完成
2025-08-02 16:15:26 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:15:37 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:15:37 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:15:37 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:15:38 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:15:38 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:15:38 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:15:38 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:15:38 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:15:38 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:15:38 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:15:38 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:15:38 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:15:38 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:15:38 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:15:38 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:15:38 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:15:38 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:15:38 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:15:38 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:15:38 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:15:38 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:15:38 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:15:38 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:15:38 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:15:38 - root - DEBUG - 数据变更事件触发
2025-08-02 16:15:40 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:15:42 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:15:42 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:15:42 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:15:42 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:15:42 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:15:42 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:15:42 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:15:42 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:15:42 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:15:42 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:15:42 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:15:42 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:15:42 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:15:42 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': ''}
2025-08-02 16:15:42 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:15:42 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:15:42 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:15:42 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:15:42 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:15:42 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:17:36 - root - INFO - 应用程序正常退出
2025-08-02 16:20:02,292 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:20:02 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:20:02 - root - INFO - 数据管理器初始化完成
2025-08-02 16:20:02 - root - INFO - Application starting...
2025-08-02 16:20:02 - root - INFO - 成功加载样式表。
2025-08-02 16:20:03 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:20:03 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:20:03 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:20:03 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:20:03 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:20:03 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:20:03 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:20:03 - root - INFO - 主窗口初始化完成
2025-08-02 16:20:03 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:20:08 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:20:08 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:20:08 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:20:08 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:20:08 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:20:08 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:20:08 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:20:08 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:20:08 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:20:08 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:20:08 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:20:08 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:20:08 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:20:08 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:20:08 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:20:08 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:20:08 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:20:08 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:20:08 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:20:08 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:20:08 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:20:08 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:20:08 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:20:08 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:20:08 - root - DEBUG - 数据变更事件触发
2025-08-02 16:20:09 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:20:11 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:20:11 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:20:11 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:20:11 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:20:11 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:20:11 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:20:11 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:20:11 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:20:11 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:20:11 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:20:11 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:20:11 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:20:11 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:20:11 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理建议-在制在购': '', '已有零件处理建议-库存': '', '已有零件处理建议-使用中': ''}
2025-08-02 16:20:11 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:20:11 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:20:11 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:20:11 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:20:11 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:20:11 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:22:17 - root - INFO - 应用程序正常退出
2025-08-02 16:36:08,186 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:36:08 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:36:08 - root - INFO - 数据管理器初始化完成
2025-08-02 16:36:08 - root - INFO - Application starting...
2025-08-02 16:36:08 - root - INFO - 成功加载样式表。
2025-08-02 16:36:09 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:36:09 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:36:09 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:36:09 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:36:09 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:36:09 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:36:09 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:36:09 - root - INFO - 主窗口初始化完成
2025-08-02 16:36:09 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:36:45 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:36:45 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:36:45 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:36:45 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:36:45 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:36:45 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:36:45 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:36:45 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:36:45 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:36:45 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:36:45 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:36:45 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:36:45 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:36:45 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:36:45 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:36:45 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:36:45 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:36:45 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:36:45 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:36:45 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:36:45 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:36:45 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:36:45 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:36:46 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:36:46 - root - DEBUG - 数据变更事件触发
2025-08-02 16:36:46 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:36:48 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:36:48 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:36:48 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:36:48 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:36:48 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:36:48 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:36:48 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:36:48 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:36:48 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:36:48 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:36:48 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:36:48 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:36:48 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:36:48 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理建议-在制在购': '', '已有零件处理建议-库存': '', '已有零件处理建议-使用中': ''}
2025-08-02 16:36:48 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:36:48 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:36:48 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:36:48 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:36:48 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:36:48 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:36:50 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:36:50 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:36:50 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:36:50 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:36:50 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:36:50 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:36:50 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:36:50 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:36:50 - root - DEBUG - self._checked_indices类型: <class 'set'>
2025-08-02 16:36:50 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:36:50 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:36:50 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:36:50 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:36:50 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理建议-在制在购': '', '已有零件处理建议-库存': '', '已有零件处理建议-使用中': ''}
2025-08-02 16:36:50 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:36:50 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:36:50 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:36:50 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:36:50 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:36:50 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:37:10 - root - INFO - 应用程序正常退出
2025-08-02 16:37:21,868 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:37:21 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:37:22 - root - INFO - 数据管理器初始化完成
2025-08-02 16:37:22 - root - INFO - Application starting...
2025-08-02 16:37:22 - root - INFO - 成功加载样式表。
2025-08-02 16:37:22 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:37:22 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:37:22 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:37:22 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:37:22 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:37:22 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:37:22 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:37:22 - root - INFO - 主窗口初始化完成
2025-08-02 16:37:22 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:37:29 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:37:29 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:37:29 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:37:29 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:37:29 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:37:29 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:37:29 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:37:29 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:37:29 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:37:29 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:37:29 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:37:29 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:37:29 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:37:29 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:37:29 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:37:29 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:37:29 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:37:29 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:37:29 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:37:29 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:37:29 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:37:29 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:37:29 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:37:30 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:37:30 - root - DEBUG - 数据变更事件触发
2025-08-02 16:37:31 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:37:33 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:37:33 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:37:33 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:37:33 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:37:33 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:37:33 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:37:33 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:37:33 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:37:33 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:37:33 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:37:33 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:37:33 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:37:33 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:37:33 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理建议-在制在购': '', '已有零件处理建议-库存': '', '已有零件处理建议-使用中': ''}
2025-08-02 16:37:33 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:37:33 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:37:33 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:37:33 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:37:33 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:37:33 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:38:04 - root - INFO - 应用程序正常退出
2025-08-02 16:41:20,282 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:41:20 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:41:20 - root - INFO - 数据管理器初始化完成
2025-08-02 16:41:20 - root - INFO - Application starting...
2025-08-02 16:41:20 - root - INFO - 成功加载样式表。
2025-08-02 16:41:21 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:41:21 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:41:21 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:41:21 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:41:21 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:41:21 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:41:21 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:41:21 - root - INFO - 主窗口初始化完成
2025-08-02 16:41:21 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:41:33 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:41:33 - root - DEBUG - 切换到标签页: 变更单管理
2025-08-02 16:41:34 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:41:35 - root - DEBUG - 切换到标签页: 变更单管理
2025-08-02 16:41:40 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:41:40 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:41:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:41:41 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:41:41 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:41:41 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:41:41 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:41:41 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:41:41 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:41:41 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:41:41 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:41:41 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:41:41 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:41:41 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:41:41 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:41:41 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:41:41 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:41:41 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:41:41 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:41:41 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:41:41 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:41:41 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:41:41 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:41:42 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:41:42 - root - DEBUG - 数据变更事件触发
2025-08-02 16:41:43 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:41:45 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:41:45 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:41:45 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:41:45 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:41:45 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:41:45 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:41:45 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:41:45 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:41:45 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:41:45 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:41:45 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:41:45 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:41:45 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:41:45 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理建议-在制在购': '', '已有零件处理建议-库存': '', '已有零件处理建议-使用中': ''}
2025-08-02 16:41:45 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:41:45 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:41:45 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:41:45 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:41:45 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:41:45 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:41:52 - root - INFO - 应用程序正常退出
2025-08-02 16:44:02,983 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:44:02 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:44:03 - root - INFO - 数据管理器初始化完成
2025-08-02 16:44:03 - root - INFO - Application starting...
2025-08-02 16:44:03 - root - INFO - 成功加载样式表。
2025-08-02 16:44:03 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:44:03 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:44:03 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:44:03 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:44:03 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:44:03 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:44:03 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:44:03 - root - INFO - 主窗口初始化完成
2025-08-02 16:44:04 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-08-02 16:44:10 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-08-02 16:44:10 - root - INFO - 开始解析变更单文档: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:44:10 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:44:10 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-08-02 16:44:10 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-08-02 16:44:10 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-08-02 16:44:10 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-08-02 16:44:10 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-08-02 16:44:10 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-08-02 16:44:10 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-08-02 16:44:10 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-08-02 16:44:10 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-08-02 16:44:10 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-08-02 16:44:10 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-08-02 16:44:10 - root - DEBUG - 正在解析第 5 行数据
2025-08-02 16:44:10 - root - ERROR - 读取'已有零件处理建议'列时出错: name 'change_cost_col' is not defined
2025-08-02 16:44:10 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-08-02 16:44:10 - root - INFO - 从文件 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中提取了 1 条记录
2025-08-02 16:44:10 - root - INFO - 变更单文档解析成功: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx, 包含 1 个条目
2025-08-02 16:44:10 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-08-02 16:44:10 - root - DEBUG - 从文档 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx 中添加了 1 个条目
2025-08-02 16:44:10 - root - INFO - 已将预解析的文档添加到管理器: 模板V2.0：工程变更单_30154_ECN-20555-240830-02.xlsx
2025-08-02 16:44:10 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-08-02 16:44:11 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:44:11 - root - DEBUG - 数据变更事件触发
2025-08-02 16:44:12 - root - DEBUG - 切换到标签页: 变更单内容
2025-08-02 16:44:14 - root - DEBUG - DataManager: get_all_items_data returning 1 items.
2025-08-02 16:44:14 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-08-02 16:44:14 - root - DEBUG - 找到的目标零件总数: 0
2025-08-02 16:44:14 - root - DEBUG - 包含'报废'值的目标零件: False
2025-08-02 16:44:14 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=0
2025-08-02 16:44:14 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-08-02 16:44:14 - root - DEBUG - 加载第 1 页数据，行数: 1
2025-08-02 16:44:14 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-08-02 16:44:14 - root - DEBUG - self._checked_indices 属性不存在
2025-08-02 16:44:14 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:44:14 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:44:14 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:44:14 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=1
2025-08-02 16:44:14 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理建议-在制在购': '', '已有零件处理建议-库存': '', '已有零件处理建议-使用中': ''}
2025-08-02 16:44:14 - root - INFO - 计算总价: 1 个项目, 总额 = 0.00
2025-08-02 16:44:14 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-08-02 16:44:14 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-08-02 16:44:14 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-08-02 16:44:14 - root - INFO - 刷新表格完成，总行数: 1
2025-08-02 16:44:14 - root - INFO - 成功获取变更单内容，共 1 条记录
2025-08-02 16:44:23 - root - INFO - 应用程序正常退出
2025-08-02 16:48:14,428 - root - INFO - 日志系统已配置。日志文件位于: logs\20250802_ecn_cost.log
2025-08-02 16:48:14 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250802_ecn_cost.log
2025-08-02 16:48:15 - root - INFO - 数据管理器初始化完成
2025-08-02 16:48:15 - root - INFO - Application starting...
2025-08-02 16:48:15 - root - INFO - 成功加载样式表。
2025-08-02 16:48:15 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-08-02 16:48:15 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-08-02 16:48:15 - root - DEBUG - 初始化带勾选框的筛选表头
2025-08-02 16:48:15 - root - DEBUG - 安排了表头的延迟更新
2025-08-02 16:48:15 - root - DEBUG - 已设置表格初始列宽
2025-08-02 16:48:15 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-08-02 16:48:15 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-08-02 16:48:15 - root - INFO - 主窗口初始化完成
2025-08-02 16:48:15 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
