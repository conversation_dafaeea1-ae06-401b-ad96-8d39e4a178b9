#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
变更单条目类模块，表示ECN变更单中的单个零件项目
"""

from typing import Dict, Any, Optional
from utils.logger import debug, error
from utils.calculation import calculator
from dataclasses import dataclass

@dataclass
class ECNItem:
    """变更单条目类，表示ECN变更单中的单个零件项目"""
    
    def __init__(self, item_data: Dict[str, Any]):
        """
        初始化变更单条目
        
        Args:
            item_data: 条目数据字典
        """
        self.data = item_data.copy()
        
        # 确保必要字段存在
        self._ensure_required_fields()
        
        # 处理数量字段格式
        self._process_quantity()
        
        # 更新金额字段
        self._update_amount()
    
    def _ensure_required_fields(self):
        """确保必要字段存在，不存在则设为默认值"""
        required_fields = {
            "零件名称": "",
            "零件编码": "",
            "ERP号": "",
            "计划号": "",
            "所属设备名称": "",
            "所属项目名称": "",
            "所属项目编号": "",
            "所属变更单单号": "",
            "新版本实施内容": "",
            "变更原因": "",
            "问题分类": "",
            "工号": "",
            "变更日期": "",
            "数量": "0",
            "已有零件处理建议-在制在购": "",
            "已有零件处理建议-库存": "",
            "已有零件处理建议-使用中": "",
            #"已有零件处理建议-变更成本": "",
            "单价": "0.00",
            "金额": "0.00"

        }
        
        for field, default_value in required_fields.items():
            if field not in self.data or self.data[field] is None:
                self.data[field] = default_value
    
    def _process_quantity(self):
        """处理数量字段格式"""
        try:
            quantity = self.data.get("数量", "0")
            if quantity:
                # 转换为浮点数再转回字符串，确保格式正确
                self.data["数量"] = str(float(quantity))
            else:
                self.data["数量"] = "0"
        except (ValueError, TypeError):
            debug(f"无效的数量值: {self.data.get('数量')}, 设为默认值 0")
            self.data["数量"] = "0"
    
    def _process_unit_price(self):
        """处理单价字段格式"""
        try:
            unit_price = self.data.get("单价", "0.00")
            if unit_price:
                # 格式化单价为指定小数位数
                self.data["单价"] = calculator.format_decimal(unit_price)
            else:
                self.data["单价"] = calculator.format_decimal(0)
        except (ValueError, TypeError):
            debug(f"无效的单价值: {self.data.get('单价')}, 设为默认值 0.00")
            self.data["单价"] = calculator.format_decimal(0)
    
    def _update_amount(self):
        """更新金额字段（数量 * 单价）"""
        try:
            quantity = self.data.get("数量", "0")
            unit_price = self.data.get("单价", "0.00")
            
            # 计算金额
            amount = calculator.calculate_amount(quantity, unit_price)
            self.data["金额"] = amount
        except Exception as e:
            error(f"更新金额出错: {str(e)}")
            self.data["金额"] = calculator.format_decimal(0)
    
    def update_unit_price(self, unit_price: str):
        """
        更新单价，并重新计算金额
        
        Args:
            unit_price: 新的单价
        """
        try:
            self.data["单价"] = calculator.format_decimal(unit_price)
            self._update_amount()
        except Exception as e:
            error(f"更新单价出错: {str(e)}")
    
    def get_data(self) -> Dict[str, Any]:
        """
        获取条目数据
        
        Returns:
            条目数据字典
        """
        return self.data.copy()
    
    def get_part_name(self) -> str:
        """获取零件名称"""
        return self.data.get("零件名称", "")
    
    def get_part_code(self) -> str:
        """获取零件编码"""
        return self.data.get("零件编码", "")
    
    def get_quantity(self) -> str:
        """获取数量"""
        return self.data.get("数量", "0")
    
    def get_unit_price(self) -> str:
        """获取单价"""
        return self.data.get("单价", "0.00")
    
    def get_unit_price_as_float(self) -> float:
        """
        获取单价的浮点数值
        
        Returns:
            单价的浮点数值，如果转换失败则返回0.0
        """
        try:
            return float(self.get_unit_price())
        except (ValueError, TypeError):
            return 0.0
    
    def get_amount(self) -> str:
        """获取金额"""
        return self.data.get("金额", "0.00")
    
    def get_erp_number(self) -> str:
        """获取ERP号"""
        return self.data.get("ERP号", "")
    
    def get_ecn_number(self) -> str:
        """获取变更单号"""
        return self.data.get("所属变更单单号", "")
    
    # 新增"已有零件处理建议"相关字段
    change_date: str = ""
    quantity: str = ""
    in_progress_purchase: str = ""
    inventory: str = ""
    in_use: str = ""
    change_cost: str = ""
    unit_price: float = 0.0
    total_price: float = 0.0
    source_file: str = ""
    part_name: str = ""
    part_code: str = ""
    erp_number: str = ""
    plan_number: str = ""
    device_name: str = ""
    project_name: str = ""
    project_number: str = ""
    implementation_content: str = ""
    change_reason: str = ""
    issue_category: str = ""
    employee_id: str = "" 