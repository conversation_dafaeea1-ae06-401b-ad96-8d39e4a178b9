#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
计算工具模块，处理金额计算等数值运算
"""

from typing import List, Dict, Union, Any
from decimal import Decimal, ROUND_HALF_UP
import config
from utils.logger import info, error, debug

class Calculator:
    """计算工具类，处理各种数值计算"""
    
    def __init__(self):
        # 金额保留小数位数
        self.decimal_places = config.DECIMAL_PLACES
    
    def calculate_amount(self, quantity: Union[str, float, int], unit_price: Union[str, float, int]) -> str:
        """
        计算金额（数量 * 单价）
        
        Args:
            quantity: 数量
            unit_price: 单价
            
        Returns:
            格式化的金额字符串
        """
        try:
            # 转换为Decimal类型进行精确计算
            qty = Decimal(str(quantity))
            price = Decimal(str(unit_price))
            
            # 计算金额
            amount = qty * price
            
            # 格式化为指定小数位数的字符串
            format_str = f'0.{self.decimal_places * "0"}'
            formatted_amount = amount.quantize(Decimal(format_str), rounding=ROUND_HALF_UP)
            
            debug(f"计算金额: {qty} * {price} = {formatted_amount}")
            return str(formatted_amount)
        except Exception as e:
            error(f"计算金额出错: {str(e)}, 数量={quantity}, 单价={unit_price}")
            return f"0.{self.decimal_places * '0'}"
    
    def calculate_total_price(self, items: List[Dict[str, Any]], price_key: str = "金额") -> str:
        """
        计算总价（所有项的金额之和）
        
        Args:
            items: 项目列表
            price_key: 金额键名
            
        Returns:
            格式化的总价字符串
        """
        try:
            total = Decimal('0')
            
            for item in items:
                price_str = item.get(price_key, '0') # 获取金额字符串，默认为'0'
                debug(f"处理项目金额: key='{price_key}', value_str='{price_str}', item_data={item}") # 新增日志
                try:
                    price = Decimal(str(price_str))      # 转换为Decimal
                    total += price
                except Exception as e: # 捕获转换异常
                    debug(f"跳过无效金额: '{price_str}' (来自key '{price_key}'). 错误: {e}")
            
            # 格式化为指定小数位数的字符串
            format_str = f'0.{self.decimal_places * "0"}'
            formatted_total = total.quantize(Decimal(format_str), rounding=ROUND_HALF_UP)
            
            info(f"计算总价: {len(items)} 个项目, 总额 = {formatted_total}")
            return str(formatted_total)
        except Exception as e:
            error(f"计算总价出错: {str(e)}")
            return f"0.{self.decimal_places * '0'}"
    
    def update_item_amount(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新单个项目的金额（数量 * 单价）
        
        Args:
            item: 项目字典
            
        Returns:
            更新后的项目字典
        """
        try:
            # 获取数量和单价
            quantity = item.get("数量", "0")
            unit_price = item.get("单价", "0")
            
            # 计算金额
            amount = self.calculate_amount(quantity, unit_price)
            
            # 更新项目字典
            item["金额"] = amount
            return item
        except Exception as e:
            error(f"更新金额出错: {str(e)}")
            item["金额"] = f"0.{self.decimal_places * '0'}"
            return item
    
    def format_decimal(self, value: Union[str, float, int]) -> str:
        """
        格式化数值为指定小数位数的字符串
        
        Args:
            value: 要格式化的数值
            
        Returns:
            格式化的字符串
        """
        try:
            decimal_value = Decimal(str(value))
            format_str = f'0.{self.decimal_places * "0"}'
            formatted_value = decimal_value.quantize(Decimal(format_str), rounding=ROUND_HALF_UP)
            return str(formatted_value)
        except Exception as e:
            error(f"格式化数值出错: {str(e)}, value={value}")
            return f"0.{self.decimal_places * '0'}"


# 创建全局计算器实例
calculator = Calculator() 