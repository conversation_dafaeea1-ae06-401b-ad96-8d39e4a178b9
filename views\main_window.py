#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口类模块，应用程序的主界面容器
"""

import os
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import (
    QMainWindow, QTabWidget, QStatusBar, QMessageBox
)
from PyQt6.QtGui import QIcon, QAction
import config
from utils.logger import info, debug, error
from views.ecn_management import ECNManagementPage
from views.ecn_content import ECNContentPage

class MainWindow(QMainWindow):
    """应用程序主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        self._init_ui()
        self._setup_connections()
        
        info("主窗口初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 设置窗口标题和大小
        self.setWindowTitle("ECN成本核算")
        self.setWindowIcon(QIcon("resources/icons/icon48px.ico"))
        self.resize(config.settings['WINDOW_WIDTH'], config.settings['WINDOW_HEIGHT'])
        
        # 创建标签页容器
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(False)
        self.tab_widget.setDocumentMode(True)
        
        # 创建变更单管理页面
        self.ecn_management_page = ECNManagementPage()
        self.tab_widget.addTab(self.ecn_management_page, "变更单管理")
        
        # 创建变更单内容页面
        self.ecn_content_page = ECNContentPage()
        self.tab_widget.addTab(self.ecn_content_page, "变更单内容")
        
        # 设置中央部件
        self.setCentralWidget(self.tab_widget)
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
        # 加载样式表
        self._load_style_sheet()
        
        # Set object name for styling
        self.setObjectName("MainWindow")

        # Load and apply stylesheet
        try:
            with open("resources/style.qss", "r", encoding="utf-8") as f:
                self.setStyleSheet(f.read())
        except FileNotFoundError:
            # You can log this or show a message if the stylesheet is critical
            print("Warning: stylesheet 'resources/style.qss' not found.")
    
    def _setup_connections(self):
        """设置信号连接"""
        # 连接标签页切换信号
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        
        # 连接数据变更信号
        self.ecn_management_page.dataChanged.connect(self._on_data_changed)
    
    def _load_style_sheet(self):
        """加载样式表"""
        try:
            style_path = config.STYLE_FILE
            if os.path.exists(style_path):
                with open(style_path, 'r', encoding='utf-8') as file:
                    style_content = file.read()
                    self.setStyleSheet(style_content)
                    debug(f"已加载样式表: {style_path}")
            else:
                error(f"样式表文件不存在: {style_path}")
        except Exception as e:
            error(f"加载样式表时出错: {str(e)}")
    
    def _on_tab_changed(self, index):
        """标签页切换事件"""
        try:
            tab_name = self.tab_widget.tabText(index)
            self.status_bar.showMessage(f"当前页面: {tab_name}")
            debug(f"切换到标签页: {tab_name}")
            
            # 如果切换到变更单内容页面，可以考虑刷新数据
            # 但通常不需要每次切换就刷新，而是由用户主动刷新
        except Exception as e:
            error(f"处理标签页切换时出错: {str(e)}")
    
    def _on_data_changed(self):
        """数据变更事件"""
        try:
            # 更新状态栏
            self.status_bar.showMessage("数据已更新")
            
            # 在这里可以根据需求执行其他操作，例如提示刷新内容页
            debug("数据变更事件触发")
        except Exception as e:
            error(f"处理数据变更事件时出错: {str(e)}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 确认是否退出
            reply = QMessageBox.question(
                self,
                "确认退出",
                "确定要退出应用程序吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 执行退出前的清理操作
                info("应用程序正常退出")
                event.accept()
            else:
                event.ignore()
        except Exception as e:
            error(f"处理窗口关闭事件时出错: {str(e)}")
            event.accept()  # 默认接受关闭事件 