#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件管理工具模块，处理文件操作和管理
"""

import os
import shutil
import hashlib
from typing import List, Tuple, Optional, Set
from PyQt6.QtWidgets import QFileDialog, QWidget
from utils.logger import info, error, debug

class FileManager:
    """文件管理类，处理文件操作"""
    
    def __init__(self):
        # 存储已添加文件的哈希值，用于去重
        self.file_hashes: Set[str] = set()
        # 存储已添加的文件路径
        self.file_paths: List[str] = []
    
    def select_excel_files(self, parent: QWidget) -> List[str]:
        """
        打开文件选择对话框，允许用户选择Excel文件
        
        Args:
            parent: 父窗口部件
            
        Returns:
            选择的文件路径列表
        """
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            parent,
            "选择ECN变更单Excel文件",
            "",
            "Excel文件 (*.xlsx *.xls)"
        )
        
        if file_paths:
            info(f"用户选择了 {len(file_paths)} 个文件")
            debug(f"选择的文件: {file_paths}")
        else:
            info("用户取消了文件选择")
            
        return file_paths
    
    def add_files(self, file_paths: List[str]) -> Tuple[List[str], List[str]]:
        """
        添加文件到管理列表，过滤重复文件
        
        Args:
            file_paths: 要添加的文件路径列表
            
        Returns:
            Tuple[添加成功的文件列表, 被过滤的重复文件列表]
        """
        added_files = []
        duplicate_files = []
        
        for file_path in file_paths:
            try:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    error(f"文件不存在: {file_path}")
                    continue
                
                # 计算文件哈希值
                file_hash = self._calculate_file_hash(file_path)
                
                # 检查是否重复
                if file_hash in self.file_hashes:
                    duplicate_files.append(file_path)
                    debug(f"跳过重复文件: {file_path}")
                    continue
                
                # 添加到管理列表
                self.file_hashes.add(file_hash)
                self.file_paths.append(file_path)
                added_files.append(file_path)
                info(f"添加文件: {file_path}")
                
            except Exception as e:
                error(f"添加文件时出错: {file_path}, 错误: {str(e)}")
        
        return added_files, duplicate_files
    
    def remove_files(self, file_paths: List[str]) -> List[str]:
        """
        从管理列表中移除文件
        
        Args:
            file_paths: 要移除的文件路径列表
            
        Returns:
            成功移除的文件列表
        """
        removed_files = []
        
        for file_path in file_paths:
            try:
                if file_path in self.file_paths:
                    # 计算文件哈希值以从哈希集合中移除
                    file_hash = self._calculate_file_hash(file_path)
                    self.file_hashes.discard(file_hash)
                    
                    # 从路径列表中移除
                    self.file_paths.remove(file_path)
                    removed_files.append(file_path)
                    info(f"移除文件: {file_path}")
                else:
                    debug(f"文件不在管理列表中: {file_path}")
            except Exception as e:
                error(f"移除文件时出错: {file_path}, 错误: {str(e)}")
        
        return removed_files
    
    def get_managed_files(self) -> List[str]:
        """
        获取当前管理的所有文件路径
        
        Returns:
            文件路径列表
        """
        return self.file_paths.copy()
    
    def clear_files(self):
        """清空所有文件记录"""
        self.file_hashes.clear()
        self.file_paths.clear()
        info("清空文件管理列表")
    
    def _calculate_file_hash(self, file_path: str, chunk_size: int = 8192) -> str:
        """
        计算文件的MD5哈希值
        
        Args:
            file_path: 文件路径
            chunk_size: 读取文件的块大小
            
        Returns:
            文件的MD5哈希值
        """
        md5 = hashlib.md5()
        
        with open(file_path, 'rb') as f:
            while chunk := f.read(chunk_size):
                md5.update(chunk)
                
        return md5.hexdigest()


# 创建全局文件管理器实例
file_manager = FileManager() 