#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Excel解析工具模块，用于解析ECN变更单Excel文件
"""

import os
import pandas as pd
import openpyxl
from typing import Dict, List, Tuple, Any, Optional
import config
from utils.logger import info, error, debug
import warnings

# from models.ecn_item import ECNItem

class ExcelParser:
    """
    Excel解析器类，用于解析ECN变更单Excel文件
    """
    
    def __init__(self):
        # 固定单元格位置配置
        self.fixed_cells = config.EXCEL_FIXED_CELLS
        # 表头位置配置
        self.headers = config.EXCEL_HEADERS
        # 数据开始行
        self.data_start_row = config.DATA_START_ROW
        # 数据结束标识
        self.data_end_marker = config.DATA_END_MARKER
    
    def parse_excel(self, file_path: str) -> Tuple[Dict[str, str], List[Dict[str, Any]]]:
        """
        解析ECN变更单Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            Tuple[文件基本信息字典, 变更单内容列表]
        """
        try:
            info(f"开始解析Excel文件: {file_path}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                error(f"文件不存在: {file_path}")
                return {}, []
            
            # 检查文件扩展名
            _, ext = os.path.splitext(file_path)
            if ext.lower() not in ['.xlsx', '.xls']:
                error(f"不支持的文件格式: {ext}")
                return {}, []
            
            # 使用openpyxl加载Excel文件
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            sheet = workbook.active
            
            # 提取文件基本信息
            basic_info = self._extract_basic_info(sheet)
            debug(f"提取的基本信息: {basic_info}")
            
            # 提取变更单内容
            items = self._extract_items(sheet)
            info(f"从文件 {os.path.basename(file_path)} 中提取了 {len(items)} 条记录")
            
            return basic_info, items
            
        except Exception as e:
            error(f"解析Excel文件时出错: {file_path}, 错误: {str(e)}")
            return {}, []
    
    def _extract_basic_info(self, sheet) -> Dict[str, str]:
        """
        从Excel中提取基本信息
        
        Args:
            sheet: Excel工作表对象
            
        Returns:
            基本信息字典
        """
        basic_info = {}
        
        try:
            for key, cell in self.fixed_cells.items():
                value = sheet[cell].value
                basic_info[key] = str(value) if value is not None else ""
                debug(f"提取基本信息: {key} = {basic_info[key]}")
        except Exception as e:
            error(f"提取基本信息时出错: {str(e)}")
        
        return basic_info
    
    def _extract_items(self, sheet) -> List[Dict[str, Any]]:
        """
        从Excel中提取变更单条目
        
        Args:
            sheet: Excel工作表对象
            
        Returns:
            变更单条目列表
        """
        items = []
        row_index = self.data_start_row
        
        # 创建列标题到列字母的映射
        column_map = {}
        for header_name, cell_ref in self.headers.items():
            col_letter = cell_ref[0]  # 从单元格引用中提取列字母
            column_map[header_name] = col_letter
        
        try:
            # 逐行读取，直到遇到结束标识
            while True:
                # 检查这一行是否是有效的数据行
                # 判断标准：1) A列必须是序号（数字）；2) C列（零件编码）不能为空且不能包含"零件编码"、"机加件"、"审核"等说明文字
                a_value = sheet[f"A{row_index}"].value
                c_value = sheet[f"C{row_index}"].value
                
                is_valid_row = True
                # 检查A列是否是数字
                try:
                    if a_value is None or not (isinstance(a_value, (int, float)) or (isinstance(a_value, str) and a_value.isdigit())):
                        is_valid_row = False
                except:
                    is_valid_row = False
                
                # 检查C列是否包含一些特殊关键词，表明这可能是说明文本而不是数据
                if c_value is None or str(c_value).strip() == "":
                    is_valid_row = False
                elif isinstance(c_value, str) and any(keyword in c_value for keyword in ["零件编码", "机加件", "审核", "批准", "填写说明","申请人"]):
                    is_valid_row = False
                
                # 如果不是有效行，则结束解析
                if not is_valid_row:
                    debug(f"在第 {row_index} 行检测到无效数据行，停止解析")
                    break
                
                debug(f"正在解析第 {row_index} 行数据")
                
                # 从该行提取数据
                item = {}
                for header_name, col_letter in column_map.items():
                    try:
                        cell_value = sheet[f"{col_letter}{row_index}"].value
                        item[header_name] = str(cell_value) if cell_value is not None else ""
                    except Exception as e:
                        error(f"读取单元格 {col_letter}{row_index} 时出错: {str(e)}")
                        item[header_name] = ""
                
                # 读取"已有零件处理建议"的三列数据
                try:
                    # 使用配置中的列字母，如果配置中不存在，使用默认值
                    in_progress_purchase_col = column_map.get('在制在购', 'O')
                    inventory_col = column_map.get('库存', 'P')
                    in_use_col = column_map.get('使用中', 'Q')

                    # 读取单元格的值
                    in_progress_purchase = sheet[f"{in_progress_purchase_col}{row_index}"].value
                    inventory = sheet[f"{inventory_col}{row_index}"].value
                    in_use = sheet[f"{in_use_col}{row_index}"].value

                    # 使用 debug 日志记录读取到的值
                    debug(f"第 {row_index} 行 '已有零件处理建议' 列的数据: " +
                          f"在制在购={in_progress_purchase}, 库存={inventory}, " +
                          f"使用中={in_use}")

                    # 添加到条目数据中 - 直接使用对应的中文键名，以便与后续处理一致
                    item["已有零件处理建议-在制在购"] = str(in_progress_purchase) if in_progress_purchase is not None else ""
                    item["已有零件处理建议-库存"] = str(inventory) if inventory is not None else ""
                    item["已有零件处理建议-使用中"] = str(in_use) if in_use is not None else ""
                except Exception as e:
                    error(f"读取'已有零件处理建议'列时出错: {str(e)}")
                    # 设置默认空值
                    item["已有零件处理建议-在制在购"] = ""
                    item["已有零件处理建议-库存"] = ""
                    item["已有零件处理建议-使用中"] = ""
                
                items.append(item)
                row_index += 1
                
                # 超过1000行时安全退出，防止无限循环
                if row_index > 1000:
                    warnings.warn(f"解析行数超过1000行，强制退出解析")
                    break
                
        except Exception as e:
            error(f"提取变更单条目时出错: {str(e)}")
        
        return items
    
    def transform_to_content_format(self, file_path: str) -> List[Dict[str, Any]]:
        """
        解析Excel文件并转换为内容页面格式
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            转换后的内容列表
        """
        # 解析Excel文件
        basic_info, items = self.parse_excel(file_path)
        
        if not basic_info or not items:
            error(f"无法从文件中提取有效数据: {file_path}")
            return []
        
        # 转换为内容页面格式
        content_items = []
        
        for item in items:
            try:
                content_item = {
                    "零件名称": item.get("名称", ""),
                    "零件编码": item.get("零件编码", ""),
                    "ERP号": basic_info.get("ERP号", ""),
                    "计划号": item.get("涉及计划号", ""),
                    "所属设备名称": basic_info.get("设备名称", ""),
                    "所属项目名称": basic_info.get("项目名称", ""),
                    "所属项目编号": basic_info.get("项目编号", ""),
                    "所属变更单单号": basic_info.get("变更单号", ""),
                    "新版本实施内容": item.get("新版本实施内容", ""),
                    "变更原因": item.get("变更原因", ""),
                    "问题分类": item.get("问题分类", ""),
                    "工号": extract_gonghao(basic_info.get("变更单号", "")),  # 从变更单号提取工号
                    "变更日期": extract_date(basic_info.get("变更单号", "")),  # 从变更单号提取日期
                    "数量": item.get("总数量", "0"),
                    "单价": "0.00",  # 单价默认为0
                    "金额": "0.00",   # 金额默认为0
                    "已有零件处理建议-在制在购": item.get("已有零件处理建议-在制在购", ""),
                    "已有零件处理建议-库存": item.get("已有零件处理建议-库存", ""),
                    "已有零件处理建议-使用中": item.get("已有零件处理建议-使用中", ""),
                    #"已有零件处理建议-变更成本": item.get("已有零件处理建议-变更成本", "")
                }
                
                # 打印调试信息，检查在程序中的键名和值
                debug(f"转换零件 {content_item['零件编码']} - {content_item['零件名称']}")
                debug(f"  内部解析键 已有零件处理建议-在制在购: {item.get('已有零件处理建议-在制在购', '')}")
                debug(f"  内部解析键 已有零件处理建议-库存: {item.get('已有零件处理建议-库存', '')}")
                debug(f"  内部解析键 已有零件处理建议-使用中: {item.get('已有零件处理建议-使用中', '')}")
                #debug(f"  内部解析键 已有零件处理建议-变更成本: {item.get('已有零件处理建议-变更成本', '')}")
                debug(f"  外部映射键 已有零件处理建议-在制在购: {content_item['已有零件处理建议-在制在购']}")
                debug(f"  外部映射键 已有零件处理建议-库存: {content_item['已有零件处理建议-库存']}")
                debug(f"  外部映射键 已有零件处理建议-使用中: {content_item['已有零件处理建议-使用中']}")
                #debug(f"  外部映射键 已有零件处理建议-变更成本: {content_item['已有零件处理建议-变更成本']}")
                
                # 尝试将数量转换为数值
                try:
                    quantity = float(content_item["数量"])
                    content_item["数量"] = str(quantity)
                except (ValueError, TypeError):
                    content_item["数量"] = "0"
                
                content_items.append(content_item)
            
            except Exception as e:
                error(f"转换数据格式时出错: {str(e)}")
        
        info(f"转换后的内容条目数量: {len(content_items)}")
        return content_items


# 辅助函数：从变更单号中提取工号
def extract_gonghao(ecn_number: str) -> str:
    """从ECN编号中提取工号"""
    try:
        # 格式: ECN-工号-日期-序号，如 ECN-20375-250115-04
        if ecn_number and "-" in ecn_number:
            parts = ecn_number.split("-")
            if len(parts) >= 2:
                return parts[1]
    except Exception as e:
        error(f"提取工号时出错: {str(e)}")
    return ""


# 辅助函数：从变更单号中提取日期
def extract_date(ecn_number: str) -> str:
    """从ECN编号中提取日期"""
    try:
        # 格式: ECN-工号-日期-序号，如 ECN-20375-250115-04
        if ecn_number and "-" in ecn_number:
            parts = ecn_number.split("-")
            if len(parts) >= 3:
                return parts[2]
    except Exception as e:
        error(f"提取日期时出错: {str(e)}")
    return ""


# 创建全局Excel解析器实例
excel_parser = ExcelParser()