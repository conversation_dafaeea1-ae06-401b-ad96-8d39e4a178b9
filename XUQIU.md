# ECN变更单管理功能
ECN变更单管理功能，单独使用一个页面，包含以下功能：
1.可以添加外部变更单表格，到当前列表中；支持多次添加，也支持一次选择并添加多个表格文件；
2.若本次添加的变更单已经存在于当前变更单列表，则跳过当前表格，不重复添加，继续添加下一个表格；
3.可以选中当前列表中的一个或多个表格，选中之后，可以进行删除操作；
4.通过添加/删除操作，形成最终的变更单列表；
5.添加文件夹功能：
  5.1 自动提取所有选中文件夹下的所有excel表格文件；
  5.2 可以将该文件夹下的所有子目录下的excel文件也提取出来；
  5.3 如果识别文件不是excel文件类型，则自动跳过；
  5.4 当选择的多个文件夹内，有表格文件名称完全相同的excel文件时，应该跳过，不要重复导入；
  5.5 多次导入时，如果前面已经导入了相同名称的包表格文件，也进行跳过，不进行重复导入；

# 变更单表格内容解析和提取功能
## 1.变更单模板说明：
    1.1 几个固定的单元格内容：
        单元格B2：当前变更单的所属ERP号；
        单元格D2：当前变更单的所属设备名称；
        单元格H2：当前变更单的所属项目编号；
        单元格K2：当前变更单的所属项目名称；
        单元格P2：当前变更单的变更单号；
    1.2 需要识别和使用的几个固定表头：
        表头A3：序号
        表头B3：变更原因
        表头C3：零件编码
        表头D3：新版本
        表头E3：名称
        表头F3：材料
        表头G3：新版本实施内容
        表头H3：数量（单台）
        表头I3：台数
        表头J3：总数量
        表头K3：涉及计划号
        表头L3：需求日期
        表头M3：问题分类
        表头N3：旧版本
        表头O3：已有零件处理建议
        表头O3会包含以下几个子表头：
           表头O4：在制在购
           表头P4：库存
           表头Q4：使用中
           表头R4：变更成本
    1.3 以上的所有表头，占用了3-4行；所以，具体的表格内容是从第5行开始，所以需要从第5行开始，读取表格内容。结束行的判断依据为：当A列读取到某一行的内容为“填写说明”时，则该行的上一行就是表格结束行；或者当A列读取到某一行的内容为空，则该行的上一行就是表格结束行。

## 2.变更单内容UI界面创建：
    2.1 创建一个变更单内容管理页面，要包含以下表头：
    表头A：零件名称----对应所属变更单的表头E3列，也就是名称；
    表头B：零件编码----对应所属变更单的表头C3列，也就是零件编码；
    表头C：ERP号----对应所属变更单的单元格B2的内容，也就是ERP号；
    表头D：计划号----对应所属变更单的表头K3列，也就是涉及计划号；
    表头E：所属设备名称----对应所属变更单的单元格D2的内容，也就是设备名称；
    表头F：所属项目名称----对应所属变更单的单元格K2的内容，也就是项目名称；
    表头G：所属项目编号----对应所属变更单的单元格H2的内容，也就是项目编号；
    表头H：所属变更单单号----对应所属变更单的表头P2列，也就是变更单编号；
    表头I：新版本实施内容----对应所属变更单的表头G3列，也就是新版本实施内容；
    表头J：变更原因----对应所属变更单的表头B3列，也就是变更原因；
    表头K：问题分类----对应所属变更单的表头M3列，也就是问题分类；
    表头L：工号----需要解读所属变更单单号的内容："ECN-"后面的数字就是工号；比如变更单单号位“ECN-22018-250109-04”，则工号为22018；
    表头M：变更日期----需要解读所属变更单单号的内容：对应工号后面的数字就是变更日期；比如变更单单号位“ECN-22018-250109-04”，则变更日期则为250109；
    表头N：数量----对应所属变更单的表头J3列，也就是总数量；
    表头O：已有零件处理意见-在制在购，对应所属变更单的表头O4列；
    表头P：已有零件处理意见-库存，对应所属变更单的表头P4列；
    表头Q：已有零件处理意见-使用中，对应所属变更单的表头Q4列；
    表头R：已有零件处理意见-变更成本，对应所属变更单的表头R4列；
    表头S：单价----需要单独获取，具体的获取逻辑需要参考后面的描述；保留2位小数点，默认是0.00；
    表头T：金额----等于J列乘以K列的值；
    2.2 每一行最左侧，都有一个勾选框，可以选中该行；第一行勾选框的上面，有一个全选框，可以选中所有筛选过滤之后的所有页面的行内容；
    2.3 每列具备筛选功能，可以进行筛选过滤；筛选小箭头放在每一列的右上角；
    2.4 表头的上方，具备以下几个控件元素：
        a.变更内容获取按钮：点击之后，会将变更单管理页面的变更单列表中的所有变更单的内容，提取到该页面中，并显示在表格中；提取的内容，就按照上述表头A-L的对应内容，一行一行提取到当前表格页面中；不在当前列表中的内容，要删除掉，只保留列表中存在的变更内容；
        b.总价显示控件：显示当前筛选过滤之后，所有的零件总价之和；也就是把过滤之后的所有L列中的数值，进行求和，并显示在控件中；保留2位小数点。
        c.选中行总价显示控件：显示当前选中行的零件总价；也就是把当前选中行的L列中的数值，进行求和，并显示在控件中；保留2位小数点。
        d.取消过滤按钮，点击之后，会取消所有列的过滤条件，将所有行内容全部显示在表格中；
    2.5 每一页最多显示100行数据，可以分多页显示；
    2.6 每一行零件的单价的获取逻辑，暂时不确定，可以先不处理；


# 开发技术要求
1.使用python语言；
2.使用Pyqt6界面；
3.使用当前目录下指定的界面风格文件：Styles.qss；
4.关键提示日志信息要全面，不要有太多无用的提示信息；
5.开发过程中，请勿使用print()函数，请使用logging模块进行日志记录；


# 零件单价获取逻辑
1.在配置代码文件"config.py"中，添加sqlserver数据库的配置信息，包含用户名、密码、主机地址、端口号、数据库名称；
2.在数据库中，有以下表和字段：
  2.1 表名称：XC_OrdinaryMaterials
      字段内容：ID、物料名称、料号、图号/型号、含税单价
  2.2 表名称：XC_RepairFee
      字段名称：ID、物料名称、料号、图号/型号、返修费备注、含税单价
3.若该行零件对应的“新版本实施内容”这一列的值不为“返修”时，单价获取逻辑为：
  3.1 “零件编码”列的内容，就对应“XC_OrdinaryMaterials”这个表的“料号”这个字段；
  3.2 表“XC_OrdinaryMaterials”这个表的“料号”字段，有可能有多个相同的料号；
  3.3 界面中“单价”列的值，就等于检索到数据库表中多个相同料号中，ID号最大的那行物料的“含税单价”字段的值；
4.若该行零件对应的“新版本实施内容”这一列的值为“返修”时，单价获取逻辑为：
  4.1 “零件编码”列的内容，就对应“XC_RepairFee”这个表的“返修费备注”这个字段；
  4.2 表“XC_RepairFee”这个表的“返修费备注”字段，有可能有多个相同的内容；
  4.3 界面中“单价”列的值，就等于检索到数据库表中多个相同“返修费备注”内容中，ID号最大的那行物料的“含税单价”字段的值；
