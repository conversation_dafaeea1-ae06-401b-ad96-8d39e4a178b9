#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据管理类模块，负责管理所有ECN变更单数据
"""

import os
import concurrent.futures
from typing import List, Dict, Any, Set, Optional, Tuple, Callable
from utils.logger import info, error, debug
from models.ecn_document import ECNDocument
from models.ecn_item import ECNItem
from utils.calculation import calculator
from utils.database_manager import DatabaseManager

class DataManager:
    """数据管理类，负责保存和维护所有变更单数据"""
    
    _instance = None
    
    def __new__(cls):
        """单例模式实现，确保全局只有一个数据管理实例"""
        if cls._instance is None:
            cls._instance = super(DataManager, cls).__new__(cls)
            cls._instance._init_data()
        return cls._instance
    
    def _init_data(self):
        """初始化数据结构"""
        # 所有的ECN文档对象
        self.ecn_documents: Dict[str, ECNDocument] = {}
        
        # 所有的ECN条目对象
        self.ecn_items: List[ECNItem] = []
        
        # 记录文件哈希，用于去重
        self.file_hashes: Set[str] = set()
        
        # 当前已选中的条目索引
        self.selected_indices: Set[int] = set()
        
        info("数据管理器初始化完成")
    
    def add_ecn_document(self, file_path: str) -> bool:
        """
        添加ECN变更单文档
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否添加成功
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                error(f"文件不存在: {file_path}")
                return False
            
            # 检查是否已添加
            if file_path in self.ecn_documents:
                debug(f"文件已存在，跳过: {file_path}")
                return False
            
            # 创建ECN文档对象
            ecn_doc = ECNDocument(file_path)
            
            # 检查解析是否成功
            if not ecn_doc.is_parsed:
                error(f"文件解析失败: {file_path}")
                return False
            
            # 添加到文档集合
            self.ecn_documents[file_path] = ecn_doc
            
            # 添加文档中的条目
            self._add_ecn_items(ecn_doc)
            
            info(f"成功添加ECN文档: {ecn_doc.file_name}, 包含 {ecn_doc.get_items_count()} 个条目")
            return True
            
        except Exception as e:
            error(f"添加ECN文档时出错: {file_path}, 错误: {str(e)}")
            return False
    
    def remove_ecn_document(self, file_path: str) -> bool:
        """
        移除ECN变更单文档及其所有条目
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否移除成功
        """
        try:
            # 检查文件是否存在于管理器中
            if file_path not in self.ecn_documents:
                debug(f"文件不存在于管理器中: {file_path}")
                return False
            
            # 获取文档对象
            ecn_doc = self.ecn_documents[file_path]
            
            # 移除与该文档相关的所有条目
            self._remove_ecn_items_by_document(ecn_doc)
            
            # 从文档集合中移除
            del self.ecn_documents[file_path]
            
            info(f"成功移除ECN文档: {os.path.basename(file_path)}")
            return True
            
        except Exception as e:
            error(f"移除ECN文档时出错: {file_path}, 错误: {str(e)}")
            return False
    
    def _add_ecn_items(self, ecn_doc: ECNDocument):
        """
        添加ECN文档中的所有条目
        
        Args:
            ecn_doc: ECN文档对象
        """
        try:
            # 获取转换后的内容条目
            content_items = ecn_doc.get_content_items()
            
            # 创建条目对象并添加到集合
            for item_data in content_items:
                ecn_item = ECNItem(item_data)
                self.ecn_items.append(ecn_item)
            
            debug(f"从文档 {ecn_doc.file_name} 中添加了 {len(content_items)} 个条目")
            
        except Exception as e:
            error(f"添加ECN条目时出错: {str(e)}")
    
    def _remove_ecn_items_by_document(self, ecn_doc: ECNDocument):
        """
        移除与指定文档相关的所有条目
        
        Args:
            ecn_doc: ECN文档对象
        """
        try:
            # 获取文档的变更单号
            ecn_number = ecn_doc.get_ecn_number()
            
            # 根据变更单号筛选出要保留的条目
            if ecn_number:
                self.ecn_items = [
                    item for item in self.ecn_items 
                    if item.get_ecn_number() != ecn_number
                ]
                debug(f"已移除变更单号为 {ecn_number} 的所有条目")
            else:
                # 如果没有变更单号，则使用文件路径来匹配
                # 这种情况不应该发生，因为每个解析成功的ECN文档应该有变更单号
                error(f"文档 {ecn_doc.file_name} 没有有效的变更单号，无法精确移除条目")
            
        except Exception as e:
            error(f"移除ECN条目时出错: {str(e)}")
    
    def get_all_documents(self) -> List[Dict[str, Any]]:
        """
        获取所有ECN文档摘要信息
        
        Returns:
            文档摘要列表
        """
        return [doc.get_summary() for doc in self.ecn_documents.values()]
    
    def get_all_items_data(self) -> List[Dict[str, Any]]:
        """
        获取所有ECN条目数据。如果缓存为空，则从所有文档中重新生成。
        """
        # 如果缓存为空，并且有文档已加载，则重新生成缓存
        if not self.ecn_items and self.ecn_documents:
            info("零件条目缓存为空，正在从所有文档中重新生成...")
            all_items = []
            for doc in self.ecn_documents.values():
                all_items.extend(doc.get_content_items())
            
            # 使用 ECNItem 类来包装数据
            self.ecn_items = [ECNItem(item_data) for item_data in all_items]
            info(f"已生成 {len(self.ecn_items)} 个零件条目缓存。")

        # 返回所有条目的数据字典列表
        # 修复：确保返回的数据包含最新的单价和金额
        all_items_data = []
        for item in self.ecn_items:
            data = item.get_data()
            data['单价'] = item.get_unit_price()
            data['金额'] = item.get_amount()
            all_items_data.append(data)

        debug(f"DataManager: get_all_items_data returning {len(all_items_data)} items.")
        return all_items_data
    
    def _get_filtered_items(self, filters: Dict[str, List[str]]) -> List[ECNItem]:
        """
        根据筛选条件获取ECN条目对象列表
        
        Args:
            filters: 筛选条件字典。
        
        Returns:
            筛选后的ECNItem对象列表
        """
        debug(f"开始筛选，条件: {filters}")
        
        if not filters:
            debug("没有筛选条件，返回所有条目")
            return self.ecn_items
            
        # 添加字段名映射，处理换行符和原始字段名不一致的问题
        field_mapping = {
            "已有零件处理建议\n-在制在购": "已有零件处理建议-在制在购",
            "已有零件处理建议\n-库存": "已有零件处理建议-库存",
            "已有零件处理建议\n-使用中": "已有零件处理建议-使用中",
            #"已有零件处理建议\n-变更成本": "已有零件处理建议-变更成本"
        }

        # 转换带换行符的过滤器键为数据模型中的实际键名
        normalized_filters = {}
        for field, filter_values in filters.items():
            if field in field_mapping:
                debug(f"筛选时使用映射字段: {field} -> {field_mapping[field]}")
                normalized_filters[field_mapping[field]] = filter_values
            else:
                normalized_filters[field] = filter_values
        
        filtered_items = []
        try:
            # 遍历所有条目
            for item in self.ecn_items:
                match = True
                # 遍历所有筛选条件
                for field, filter_values in normalized_filters.items():
                    item_data = item.get_data()
                    value = item_data.get(field)
                    
                    # 如果筛选值为列表
                    if isinstance(filter_values, list):
                        # 如果一个字段的筛选列表为空，意味着用户取消了所有勾选，因此不应匹配任何条目
                        if not filter_values:
                            match = False
                            break

                        # 检查值是否在列表中 (统一转为字符串比较)
                        if str(value) not in [str(v) for v in filter_values]:
                            match = False
                            break
                    # 如果筛选值为字符串 (用于文本搜索，暂未实现)
                    else:
                        if str(filter_values).lower() not in str(value).lower():
                            match = False
                            break
                
                if match:
                    filtered_items.append(item)
                    
            debug(f"筛选结果: 共 {len(filtered_items)} / {len(self.ecn_items)} 条符合条件")
            return filtered_items
        except Exception as e:
            error(f"筛选条目时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())
            return []

    def get_filtered_items_data(self, filters=None):
        """
        获取经过筛选的零件数据列表
        
        Args:
            filters: 筛选条件字典，如果为None，则应用空筛选（返回所有）
        
        Returns:
            筛选后的零件数据字典列表
        """
        # 确保总是传递一个字典给_get_filtered_items
        active_filters = filters if filters is not None else {}
        items = self._get_filtered_items(active_filters)
        
        result = []
        for item in items:
            # 基于原始数据创建新的数据字典
            data = item.get_data().copy()
            
            # 获取单价，确保是数值类型
            unit_price = item.get_unit_price_as_float()
            
            # 获取数量，确保是数值类型
            quantity = data.get('数量', 0)
            if isinstance(quantity, str):
                try:
                    quantity = float(quantity)
                except (ValueError, TypeError):
                    quantity = 0.0
            
            # 计算金额
            amount = unit_price * quantity
            
            # 更新字典中的单价和金额
            data['单价'] = f"{unit_price:.2f}"
            data['金额'] = f"{amount:.2f}"
            
            result.append(data)
        
        return result
    
    def set_selected_indices(self, indices: List[int]):
        """
        设置已选中的条目索引
        
        Args:
            indices: 所选条目的索引列表
        """
        self.selected_indices = set(indices)
    
    def get_selected_items_data(self) -> List[Dict[str, Any]]:
        """
        获取已选中条目的数据
        
        Returns:
            所选条目的数据列表
        """
        selected_items = []
        filtered_items = self.get_filtered_items_data()
        
        for idx in self.selected_indices:
            if 0 <= idx < len(filtered_items):
                selected_items.append(filtered_items[idx])
        
        return selected_items
    
    def calculate_total_amount(self, selected_only: bool = False) -> str:
        """
        计算总金额
        
        Args:
            selected_only: 是否只计算选中条目的金额
            
        Returns:
            格式化的总金额字符串
        """
        items = self.get_selected_items_data() if selected_only else self.get_filtered_items_data()
        return calculator.calculate_total_price(items)
    
    def clear_all_data(self):
        """清空所有数据"""
        self.ecn_documents.clear()
        self.ecn_items.clear()
        self.file_hashes.clear()
        self.selected_indices.clear()
        info("已清空所有数据")
    
    def update_item_unit_price(self, index: int, unit_price: str) -> bool:
        """
        更新指定索引条目的单价
        
        Args:
            index: 条目索引
            unit_price: 新的单价
            
        Returns:
            是否更新成功
        """
        try:
            filtered_items = self.get_filtered_items_data()
            
            if 0 <= index < len(filtered_items):
                # 找到对应的ECNItem对象
                item_data = filtered_items[index]
                
                # 在所有条目中查找匹配的条目
                for item in self.ecn_items:
                    if (item.get_part_code() == item_data["零件编码"] and
                        item.get_ecn_number() == item_data["所属变更单单号"]):
                        
                        # 更新单价并重新计算金额
                        item.update_unit_price(unit_price)
                        info(f"更新条目单价成功: 零件编码={item.get_part_code()}, 单价={unit_price}")
                        return True
                
                error(f"未找到匹配的条目: 索引={index}")
                return False
            else:
                error(f"条目索引超出范围: {index}")
                return False
                
        except Exception as e:
            error(f"更新条目单价时出错: {str(e)}")
            return False
    
    def update_prices_from_db(self) -> int:
        """
        从数据库更新所有当前零件的单价
        
        Returns:
            成功更新单价的零件数量。如果连接失败，返回 -1。
        """
        if not self.ecn_items:
            info("没有要更新单价的零件数据")
            return 0

        # 首先尝试连接，如果失败则返回-1
        if not DatabaseManager.connect():
            return -1

        updated_count = 0
        try:
            info("开始从数据库批量更新零件单价...")
            for item in self.ecn_items:
                part_code = item.get_part_code()
                if not part_code:
                    continue

                item_data = item.get_data()
                implementation_content = item_data.get("新版本实施内容", "")
                is_repair = "返修" in implementation_content

                # 从数据库获取价格
                price = DatabaseManager.fetch_unit_price(part_code, is_repair)

                if price is not None:
                    item.update_unit_price(price)
                    updated_count += 1
            
            info(f"单价更新完成，共更新 {updated_count} 个零件的单价")
            return updated_count
        except Exception as e:
            error(f"从数据库更新单价时出错: {e}")
            return 0
        finally:
            # 确保数据库连接总是被关闭
            DatabaseManager.disconnect()

    def _parse_single_file_for_worker(self, file_path: str) -> Optional[ECNDocument]:
        """
        供后台工作线程调用的单个文件解析方法。
        注意：这个方法是线程安全的，因为它不修改 DataManager 的状态。
        """
        try:
            if file_path in self.ecn_documents:
                debug(f"文件 '{os.path.basename(file_path)}' 已存在，跳过解析。")
                return None
            
            doc = ECNDocument(file_path)
            if doc.is_parsed:
                return doc
        except Exception as e:
            error(f"解析文件 '{file_path}' 时出错: {e}")
        return None

    def parse_files_in_background(self, file_paths: List[str], update_progress: Callable) -> List[Tuple[str, bool, str]]:
        """
        在后台并发解析多个Excel文件，并返回每个文件的处理结果。

        Args:
            file_paths: 要解析的文件路径列表。
            update_progress: 用于更新进度的回调函数。

        Returns:
            一个列表，每个元素是一个元组 (file_path, success, message)
        """
        results = []
        parsed_docs = []

        for i, file_path in enumerate(file_paths):
            try:
                if file_path in self.ecn_documents:
                    message = "文件已存在，已跳过。"
                    results.append((file_path, False, message))
                    debug(f"文件 '{os.path.basename(file_path)}' 已存在，跳过解析。")
                else:
                    doc = ECNDocument(file_path)
                    if doc.is_parsed:
                        parsed_docs.append(doc)
                        results.append((file_path, True, "成功"))
                    else:
                        # 从日志或ECNDocument内部获取更详细的错误
                        message = "文件解析失败，可能是格式问题或内容不符合模板。"
                        results.append((file_path, False, message))
            except Exception as e:
                error_msg = f"解析时发生意外错误: {str(e)}"
                error(f"解析文件 '{file_path}' 时出错: {error_msg}")
                results.append((file_path, False, error_msg))
            
            # 更新进度
            update_progress(i + 1)
        
        # 所有文件解析完毕后，统一将成功解析的文档添加到数据管理器中
        if parsed_docs:
            self.add_parsed_documents(parsed_docs)

        return results

    def add_parsed_documents(self, documents: List[ECNDocument]):
        """
        将已成功解析的ECNDocument对象列表添加到数据管理器中
        
        Args:
            documents: ECNDocument对象列表
        """
        for doc in documents:
            if doc.file_path not in self.ecn_documents:
                self.ecn_documents[doc.file_path] = doc
                self._add_ecn_items(doc)
                info(f"已将预解析的文档添加到管理器: {doc.file_name}")

    def _fetch_single_item_price_worker(self, item: ECNItem) -> bool:
        """
        在工作线程中为单个条目获取价格。
        此方法会创建自己的数据库连接。
        """
        db_manager = DatabaseManager()
        try:
            if not db_manager.connect():
                error(f"工作线程无法连接到数据库 (零件: {item.get_part_code()})")
                return False

            item_data = item.get_data()
            part_code = item_data.get("零件编码")
            if not part_code:
                return False

            implementation_content = item_data.get("新版本实施内容", "")
            is_repair = "返修" in implementation_content

            # 从数据库获取价格
            price = db_manager.fetch_unit_price(part_code, is_repair)

            if price is not None:
                item.update_unit_price(str(price))
                return True
            return False
        except Exception as e:
            error(f"获取零件 {item.get_part_code()} 单价时线程出错: {e}")
            return False
        finally:
            if db_manager:
                db_manager.disconnect()

    def fetch_prices_in_background(self, update_progress: Callable) -> int:
        """
        在后台并发更新所有当前零件的单价
        """
        if not self.ecn_items:
            info("没有要更新单价的零件数据")
            return 0

        updated_count = 0
        total_items = len(self.ecn_items)

        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            # 直接将 ECNItem 对象传递给工作函数
            future_to_item = {executor.submit(self._fetch_single_item_price_worker, item): item for item in self.ecn_items}

            for i, future in enumerate(concurrent.futures.as_completed(future_to_item)):
                item = future_to_item[future]
                try:
                    if future.result():
                        updated_count += 1
                except Exception as exc:
                    error(f"为零件 {item.get_part_code()} 获取价格的任务生成异常: {exc}")

                # 更新进度
                update_progress(i + 1)

        info(f"单价更新完成，共更新 {updated_count} 个零件的单价")
        return updated_count

    def clear_items_cache(self):
        """
        清空零件条目缓存。
        """
        self.ecn_items = []
        debug("零件条目缓存已清除。")


# 创建全局数据管理器实例
data_manager = DataManager()