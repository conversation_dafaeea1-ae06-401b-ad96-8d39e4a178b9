2025-06-30 13:47:45 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250630_ecn_cost.log
2025-06-30 13:48:02 - root - INFO - 数据管理器初始化完成
2025-06-30 13:48:02 - root - INFO - Application starting...
2025-06-30 13:48:02 - root - INFO - 成功加载样式表。
2025-06-30 13:48:03 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-06-30 13:48:03 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-06-30 13:48:03 - root - DEBUG - 初始化带勾选框的筛选表头
2025-06-30 13:48:03 - root - DEBUG - 安排了表头的延迟更新
2025-06-30 13:48:03 - root - DEBUG - 已设置表格初始列宽
2025-06-30 13:48:03 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-06-30 13:48:03 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-06-30 13:48:03 - root - INFO - 主窗口初始化完成
2025-06-30 13:48:03 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-06-30 13:48:09 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:48:10 - root - DEBUG - 切换到标签页: 变更单管理
2025-06-30 13:48:11 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:48:18 - root - DEBUG - 切换到标签页: 变更单管理
2025-06-30 13:48:19 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:48:21 - root - DEBUG - 切换到标签页: 变更单管理
2025-06-30 13:48:23 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:48:27 - root - DEBUG - 切换到标签页: 变更单管理
2025-06-30 13:48:39 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-06-30 13:48:39 - root - INFO - 开始解析变更单文档: 工程变更单_20062_ECN-20023-250126-10.xlsx
2025-06-30 13:48:39 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20062_ECN-20023-250126-10.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20062
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = Busbar切平
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA005
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = 广汽锐湃IDU定子装配线
（第一条线）
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20023-250126-10
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20062', '设备名称': 'Busbar切平', '项目编号': 'CN23HA005', '项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '变更单号': 'ECN-20023-250126-10'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 7 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20062_ECN-20023-250126-10.xlsx 中提取了 2 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20062_ECN-20023-250126-10.xlsx, 包含 2 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20063_ECN-20423-250424-23.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20063_ECN-20423-250424-23.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20063
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 线体集成
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA005
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = 广汽锐湃IDU定子装配线（一线）
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20423-250424-23
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20063', '设备名称': '线体集成', '项目编号': 'CN23HA005', '项目名称': '广汽锐湃IDU定子装配线（一线）', '变更单号': 'ECN-20423-250424-23'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 8 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 9 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 9 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 10 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 10 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 11 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 11 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 12 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 12 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 13 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 13 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 14 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 14 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 15 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 15 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 16 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 16 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 17 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 17 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 18 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20063_ECN-20423-250424-23.xlsx 中提取了 13 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20063_ECN-20423-250424-23.xlsx, 包含 13 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250115-04.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250115-04.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250115-04
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250115-04'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 8 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 9 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 9 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 10 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 10 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 11 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250115-04.xlsx 中提取了 6 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250115-04.xlsx, 包含 6 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250126-07.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250126-07.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250126-07
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250126-07'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 8 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 9 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 9 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 10 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 10 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 11 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 11 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 12 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 12 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 13 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 13 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 14 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 14 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 15 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 15 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 16 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250126-07.xlsx 中提取了 11 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250126-07.xlsx, 包含 11 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250219-02.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250219-02.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250219-02
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250219-02'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 8 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 9 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 9 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 10 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 10 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 11 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 11 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 12 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 12 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 13 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 13 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 14 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 14 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 15 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250219-02.xlsx 中提取了 10 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250219-02.xlsx, 包含 10 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250306-04.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250306-04.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250306-04
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250306-04'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 8 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 9 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250306-04.xlsx 中提取了 4 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250306-04.xlsx, 包含 4 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250325-12.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250325-12.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250325-12
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250325-12'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 8 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 9 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 9 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 10 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250325-12.xlsx 中提取了 5 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250325-12.xlsx, 包含 5 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250328-16.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250328-16.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250328-16
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250328-16'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 8 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 9 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 9 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 10 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 10 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 11 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 11 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 12 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 12 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 13 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 13 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 14 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 14 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 15 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 15 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 16 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 16 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 17 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250328-16.xlsx 中提取了 12 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250328-16.xlsx, 包含 12 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250514-10.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250514-10.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250514-10
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250514-10'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 8 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250514-10.xlsx 中提取了 3 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250514-10.xlsx, 包含 3 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250523-19.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250523-19.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250523-19
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250523-19'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=返修, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=返修, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 8 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250523-19.xlsx 中提取了 3 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250523-19.xlsx, 包含 3 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20375-250530-26.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20375-250530-26.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20375-250530-26
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20375-250530-26'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 6 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20375-250530-26.xlsx 中提取了 1 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20375-250530-26.xlsx, 包含 1 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20065_ECN-20555-250318-05.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20065_ECN-20555-250318-05.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20065
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 插纸机
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20555-250318-05
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20065', '设备名称': '插纸机', '项目编号': 'CN23HA006', '项目名称': 'APP550 stator assembly line', '变更单号': 'ECN-20555-250318-05'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 8 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 9 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 9 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=返修, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 10 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 10 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=返修, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 11 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 11 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 12 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 12 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=报废, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 13 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 13 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 14 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20065_ECN-20555-250318-05.xlsx 中提取了 9 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20065_ECN-20555-250318-05.xlsx, 包含 9 个条目
2025-06-30 13:48:40 - root - INFO - 开始解析变更单文档: 工程变更单_20066_ECN-20074-250312-03.xlsx
2025-06-30 13:48:40 - root - INFO - 开始解析Excel文件: G:/Uptec Working/develop/ECN_Cost/变更单/工程变更单_20066_ECN-20074-250312-03.xlsx
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-06-30 13:48:40 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: ERP号 = 20066
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 设备名称 = 线成型
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA006
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 项目名称 = APP550 Stator assembly line
2025-06-30 13:48:40 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20074-250312-03
2025-06-30 13:48:40 - root - DEBUG - 提取的基本信息: {'ERP号': '20066', '设备名称': '线成型', '项目编号': 'CN23HA006', '项目名称': 'APP550 Stator assembly line', '变更单号': 'ECN-20074-250312-03'}
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 5 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 6 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=换图加工, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 正在解析第 7 行数据
2025-06-30 13:48:40 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=换图加工, 库存=None, 使用中=None, 变更成本=None
2025-06-30 13:48:40 - root - DEBUG - 在第 8 行检测到无效数据行，停止解析
2025-06-30 13:48:40 - root - INFO - 从文件 工程变更单_20066_ECN-20074-250312-03.xlsx 中提取了 3 条记录
2025-06-30 13:48:40 - root - INFO - 变更单文档解析成功: 工程变更单_20066_ECN-20074-250312-03.xlsx, 包含 3 个条目
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20062_ECN-20023-250126-10.xlsx 中添加了 2 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20062_ECN-20023-250126-10.xlsx
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 8.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 8.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20063_ECN-20423-250424-23.xlsx 中添加了 13 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20063_ECN-20423-250424-23.xlsx
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-130 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-340 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-205 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-206 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250115-04.xlsx 中添加了 6 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250115-04.xlsx
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-08-039 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250126-07.xlsx 中添加了 11 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250126-07.xlsx
2025-06-30 13:48:40 - root - DEBUG - 零件 20069-04-006 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 零件 20069-04-007 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 零件 20069-04-023 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 零件 20069-04-022 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250219-02.xlsx 中添加了 10 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250219-02.xlsx
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250306-04.xlsx 中添加了 4 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250306-04.xlsx
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250325-12.xlsx 中添加了 5 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250325-12.xlsx
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250328-16.xlsx 中添加了 12 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250328-16.xlsx
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250514-10.xlsx 中添加了 3 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250514-10.xlsx
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-206 的'已有零件处理意见-使用中'值: '返修'
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-205 的'已有零件处理意见-使用中'值: '返修'
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250523-19.xlsx 中添加了 3 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250523-19.xlsx
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20375-250530-26.xlsx 中添加了 1 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20375-250530-26.xlsx
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-201 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-263 的'已有零件处理意见-使用中'值: '返修'
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-264 的'已有零件处理意见-使用中'值: '返修'
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-098 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 零件 20065-04-092 的'已有零件处理意见-使用中'值: '报废'
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 1.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20065_ECN-20555-250318-05.xlsx 中添加了 9 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20065_ECN-20555-250318-05.xlsx
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 3.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 3.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 计算金额: 3.0 * 0.00 = 0.00
2025-06-30 13:48:40 - root - DEBUG - 从文档 工程变更单_20066_ECN-20074-250312-03.xlsx 中添加了 3 个条目
2025-06-30 13:48:40 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20066_ECN-20074-250312-03.xlsx
2025-06-30 13:48:40 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-06-30 13:48:41 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-06-30 13:48:41 - root - DEBUG - 数据变更事件触发
2025-06-30 13:48:44 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:48:46 - root - DEBUG - DataManager: get_all_items_data returning 82 items.
2025-06-30 13:48:46 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-06-30 13:48:46 - root - DEBUG - 找到目标零件 20065-04-130, '已有零件处理意见-使用中'值 = '报废'
2025-06-30 13:48:46 - root - DEBUG - 找到目标零件 20065-04-340, '已有零件处理意见-使用中'值 = '报废'
2025-06-30 13:48:46 - root - DEBUG - 找到目标零件 20065-04-205, '已有零件处理意见-使用中'值 = '报废'
2025-06-30 13:48:46 - root - DEBUG - 找到目标零件 20065-04-206, '已有零件处理意见-使用中'值 = '报废'
2025-06-30 13:48:46 - root - DEBUG - 找到目标零件 20065-04-206, '已有零件处理意见-使用中'值 = '返修'
2025-06-30 13:48:46 - root - DEBUG - 找到目标零件 20065-04-205, '已有零件处理意见-使用中'值 = '返修'
2025-06-30 13:48:46 - root - DEBUG - 找到的目标零件总数: 6
2025-06-30 13:48:46 - root - DEBUG - 包含'报废'值的目标零件: True
2025-06-30 13:48:46 - root - DEBUG -   零件 20065-04-130: 使用中值 = '报废'
2025-06-30 13:48:46 - root - DEBUG -   零件 20065-04-340: 使用中值 = '报废'
2025-06-30 13:48:46 - root - DEBUG -   零件 20065-04-205: 使用中值 = '报废'
2025-06-30 13:48:46 - root - DEBUG -   零件 20065-04-206: 使用中值 = '报废'
2025-06-30 13:48:46 - root - DEBUG -   零件 20065-04-206: 使用中值 = '返修'
2025-06-30 13:48:46 - root - DEBUG -   零件 20065-04-205: 使用中值 = '返修'
2025-06-30 13:48:46 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=81
2025-06-30 13:48:46 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-06-30 13:48:46 - root - DEBUG -   要加载到表格的目标零件 20065-04-130: 使用中值='报废'
2025-06-30 13:48:46 - root - DEBUG -   要加载到表格的目标零件 20065-04-340: 使用中值='报废'
2025-06-30 13:48:46 - root - DEBUG -   要加载到表格的目标零件 20065-04-205: 使用中值='报废'
2025-06-30 13:48:46 - root - DEBUG -   要加载到表格的目标零件 20065-04-206: 使用中值='报废'
2025-06-30 13:48:46 - root - DEBUG -   要加载到表格的目标零件 20065-04-206: 使用中值='返修'
2025-06-30 13:48:46 - root - DEBUG -   要加载到表格的目标零件 20065-04-205: 使用中值='返修'
2025-06-30 13:48:46 - root - DEBUG - 在当前页找到目标零件:
零件 20065-04-130: 值='报废', 行索引=17'
零件 20065-04-340: 值='报废', 行索引=18'
零件 20065-04-205: 值='返修', 行索引=68'
零件 20065-04-206: 值='返修', 行索引=67'
2025-06-30 13:48:46 - root - DEBUG - 加载第 1 页数据，行数: 82
2025-06-30 13:48:46 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-06-30 13:48:46 - root - DEBUG - self._checked_indices 属性不存在
2025-06-30 13:48:46 - root - DEBUG - 已设置表格初始列宽
2025-06-30 13:48:46 - root - INFO - 刷新表格完成，总行数: 82
2025-06-30 13:48:46 - root - DEBUG - 已设置表格初始列宽
2025-06-30 13:48:46 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=82
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平座', '零件编码': '20062-04-000-01', 'ERP号': '20062', '计划号': 'SCJH20230251\nSCJH20230252', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '20062-04-049', 'ERP号': '20062', '计划号': 'SCJH20230251\nSCJH20230252', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '线成型铭牌', '零件编码': '20063-99-112', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '8.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '线体集成铭牌', '零件编码': '20063-99-128', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '自动插线铭牌', '零件编码': '20063-99-113', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '8.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '绕组转运及插入铭牌', '零件编码': '20063-99-114', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '扩口及扩口仿形检查铭牌', '零件编码': '20063-99-117', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '扭头及扭头仿形检查铭牌', '零件编码': '20063-99-118', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '清洁翻转铭牌', '零件编码': '20063-99-116', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平焊接工装自动夹装铭牌', '零件编码': '20063-99-120', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': 'busbar切平铭牌', '零件编码': '20063-99-127', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '漆前绝检测和PDIV检测铭牌', '零件编码': '20063-99-126', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '铁芯上下料铭牌', '零件编码': '20063-99-108', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '铁芯在线检测铭牌', '零件编码': '20063-99-109', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '差绝缘纸及热扩口铭牌', '零件编码': '20063-99-110', 'ERP号': '20063', '计划号': 'SCJH20230253', '所属设备名称': '线体集成', '所属项目名称': '广汽锐湃IDU定子装配线（一线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20423-250424-23', '新版本实施内容': '增加', '变更原因': '调试备件', '问题分类': '107', '工号': '20423', '变更日期': '250424', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '成型模', '零件编码': '20065-04-000-01', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250115-04', '新版本实施内容': '换图装配', '变更原因': '调节钣金更换样式', '问题分类': '101', '工号': '20375', '变更日期': '250115', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': 'O成型', '零件编码': '20065-04-000-09', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250115-04', '新版本实施内容': '换图装配', '变更原因': '导向不好，更新零件', '问题分类': '101', '工号': '20375', '变更日期': '250115', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '传感器支架', '零件编码': '20065-04-130', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250115-04', '新版本实施内容': '报废重投', '变更原因': '调节钣金更换样式', '问题分类': '101', '工号': '20375', '变更日期': '250115', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '光电支架1', '零件编码': '20065-04-340', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250115-04', '新版本实施内容': '报废重投', '变更原因': '调节钣金调节方向更改', '问题分类': '101', '工号': '20375', '变更日期': '250115', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '左侧限纸块', '零件编码': '20065-04-205', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250115-04', '新版本实施内容': '报废重投', '变更原因': '导向不好，更新零件', '问题分类': '101', '工号': '20375', '变更日期': '250115', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '右侧限纸块', '零件编码': '20065-04-206', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250115-04', '新版本实施内容': '报废重投', '变更原因': '导向不好，更新零件', '问题分类': '101', '工号': '20375', '变更日期': '250115', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '上升机构', '零件编码': '20065-08-000-02', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '换图装配', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '定子上下料', '零件编码': '20065-08-000', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '换图装配', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '顶升旋转组件', '零件编码': '20065-06-000', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '换图装配', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '接废料机构', '零件编码': '20065-09-000', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '换图装配', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '升降翻转机构', '零件编码': '20081-05-000', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '换图装配', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '指针片', '零件编码': '20065-08-052', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '增加', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '限位块', '零件编码': '20065-08-053', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '增加', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '感应片', '零件编码': '20065-08-039', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '报废重投', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '指示钣金', '零件编码': '20065-06-051', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '增加', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '指示片', '零件编码': '20065-09-037', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '增加', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '原点指针', '零件编码': '20081-05-014', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250126-07', '新版本实施内容': '增加', '变更原因': '增加指示钣金', '问题分类': '101', '工号': '20375', '变更日期': '250126', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '升降翻转机构', '零件编码': '20081-05-000', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '换图装配', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '挡圈', '零件编码': '20069-04-050', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '增加', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '连接轴', '零件编码': '20069-04-051', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '增加', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '转轴盘', '零件编码': '20069-04-052', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '增加', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '安装块', '零件编码': '20081-05-016', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '增加', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '定位销', '零件编码': '20081-05-017', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '增加', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '转轴盘', '零件编码': '20069-04-006', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '减少', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '转轴', '零件编码': '20069-04-007', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '减少', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '定位销', '零件编码': '20069-04-023', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '减少', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '安装块', '零件编码': '20069-04-022', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250219-02', '新版本实施内容': '减少', '变更原因': '减速机与轴连接方式不合理', '问题分类': '101', '工号': '20375', '变更日期': '250219', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '插纸上框架', '零件编码': '20065-02-000', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250306-04', '新版本实施内容': '换图装配', '变更原因': '增加油泵支架', '问题分类': '101', '工号': '20375', '变更日期': '250306', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '油泵固定板', '零件编码': '20065-02-060', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250306-04', '新版本实施内容': '增加', '变更原因': '增加油泵支架', '问题分类': '101', '工号': '20375', '变更日期': '250306', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '接油盘', '零件编码': '20090-92-114', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250306-04', '新版本实施内容': '增加', '变更原因': '增加油泵支架', '问题分类': '101', '工号': '20375', '变更日期': '250306', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '小型球阀BBRPT11', '零件编码': '30020681455', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250306-04', '新版本实施内容': '增加', '变更原因': '增加油泵支架', '问题分类': '101', '工号': '20375', '变更日期': '250306', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '插纸上框架', '零件编码': '20065-02-000', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250325-12', '新版本实施内容': '换图装配', '变更原因': '增加切纸刀', '问题分类': '101', '工号': '20375', '变更日期': '250325', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切纸刀组件', '零件编码': '20065-02-000-10', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250325-12', '新版本实施内容': '增加', '变更原因': '增加切纸刀', '问题分类': '101', '工号': '20375', '变更日期': '250325', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切纸刀', '零件编码': '20065-02-061', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250325-12', '新版本实施内容': '增加', '变更原因': '增加切纸刀', '问题分类': '101', '工号': '20375', '变更日期': '250325', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '保护罩', '零件编码': '20065-02-058', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250325-12', '新版本实施内容': '增加', '变更原因': '增加切纸刀', '问题分类': '101', '工号': '20375', '变更日期': '250325', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '废纸盒', '零件编码': '20065-02-059', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250325-12', '新版本实施内容': '增加', '变更原因': '增加切纸刀', '问题分类': '101', '工号': '20375', '变更日期': '250325', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '铭牌组件', '零件编码': '20065-02-000-09', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '换图装配', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '6号电磁阀铭牌', '零件编码': '20065-02-063', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '36号电磁阀铭牌', '零件编码': '20065-02-064', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '36气缸铭牌', '零件编码': '20065-02-065', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '36阀岛铭牌', '零件编码': '20065-02-066', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '12号电磁阀铭牌', '零件编码': '20065-02-067', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '19号电磁阀铭牌', '零件编码': '20065-02-068', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '20号电磁阀铭牌', '零件编码': '20065-02-069', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '23号电磁阀铭牌', '零件编码': '20065-02-070', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '24号电磁阀铭牌', '零件编码': '20065-02-071', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '29号电磁阀铭牌', '零件编码': '20065-02-072', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '35号电磁阀铭牌', '零件编码': '20065-02-073', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250328-16', '新版本实施内容': '增加', '变更原因': '气路图铭牌更新', '问题分类': '101', '工号': '20375', '变更日期': '250328', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '成型模', '零件编码': '20065-04-000-01', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '换图装配', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '凹模', '零件编码': '20065-04-356', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '凹模验证', '问题分类': '101', '工号': '20375', '变更日期': '250514', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '成型模平模', '零件编码': '20065-04-165', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250514-10', '新版本实施内容': '增加', '变更原因': '增加备件', '问题分类': '107', '工号': '20375', '变更日期': '250514', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': 'O成型', '零件编码': '20065-04-000-09', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250523-19', '新版本实施内容': '换图装配', '变更原因': '过纸槽加大', '问题分类': '101', '工号': '20375', '变更日期': '250523', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '右侧限纸块', '零件编码': '20065-04-206', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250523-19', '新版本实施内容': '返修', '变更原因': '过纸槽加大', '问题分类': '101', '工号': '20375', '变更日期': '250523', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '返修', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '左侧限纸块', '零件编码': '20065-04-205', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250523-19', '新版本实施内容': '返修', '变更原因': '过纸槽加大', '问题分类': '101', '工号': '20375', '变更日期': '250523', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '返修', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '标尺_BLR61-L300', '零件编码': '30020740069', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20375-250530-26', '新版本实施内容': '增加', '变更原因': '现场损坏', '问题分类': '107', '工号': '20375', '变更日期': '250530', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': 'O成型', '零件编码': '20065-04-000-09', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '换图装配', '变更原因': '推杆与成型槽间隙过大，晃动磨损。', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '推纸机构', '零件编码': '20065-04-000-13', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '换图装配', '变更原因': '增加推杆位置检测', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '成型块', '零件编码': '20065-04-201', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '报废重投', '变更原因': '推杆与成型槽间隙过大，晃动磨损。', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '传感器支架', '零件编码': '20065-04-355', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '增加', '变更原因': '增加推杆位置检测', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '推杆连接块', '零件编码': '20065-04-263', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '返修', '变更原因': '推杆与成型槽间隙过大，晃动磨损。', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '返修', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '压板', '零件编码': '20065-04-264', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '返修', '变更原因': '推杆与成型槽间隙过大，晃动磨损。', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '返修', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '顶紧螺钉', '零件编码': '20065-04-098', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '报废重投', '变更原因': '弹簧间隙过大，压紧后损坏。', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '推杆', '零件编码': '20065-04-092', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '报废重投', '变更原因': '推杆与成型槽间隙过大，晃动磨损。', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '报废', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '激光传感器LR-X100C', '零件编码': '30020060005', 'ERP号': '20065', '计划号': 'SCJH20230302', '所属设备名称': '插纸机', '所属项目名称': 'APP550 stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20555-250318-05', '新版本实施内容': '增加', '变更原因': '增加推杆位置检测', '问题分类': '101', '工号': '20555', '变更日期': '250318', '数量': '1.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切断模组总装', '零件编码': '20066-24-000', 'ERP号': '20066', '计划号': 'SCJH20230303～SCJH20230305', '所属设备名称': '线成型', '所属项目名称': 'APP550 Stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20074-250312-03', '新版本实施内容': '换图装配', '变更原因': '2D切断刀倒角不明显', '问题分类': '105', '工号': '20074', '变更日期': '250312', '数量': '3.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '下刀座', '零件编码': '20066-24-063', 'ERP号': '20066', '计划号': 'SCJH20230303～SCJH20230305', '所属设备名称': '线成型', '所属项目名称': 'APP550 Stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20074-250312-03', '新版本实施内容': '换图加工', '变更原因': '2D切断刀倒角不明显', '问题分类': '105', '工号': '20074', '变更日期': '250312', '数量': '3.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '换图加工', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切刀', '零件编码': '20066-24-064', 'ERP号': '20066', '计划号': 'SCJH20230303～SCJH20230305', '所属设备名称': '线成型', '所属项目名称': 'APP550 Stator assembly line', '所属项目编号': 'CN23HA006', '所属变更单单号': 'ECN-20074-250312-03', '新版本实施内容': '换图加工', '变更原因': '2D切断刀倒角不明显', '问题分类': '105', '工号': '20074', '变更日期': '250312', '数量': '3.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '换图加工', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-06-30 13:48:46 - root - INFO - 计算总价: 82 个项目, 总额 = 0.00
2025-06-30 13:48:46 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-06-30 13:48:46 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-06-30 13:48:46 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-06-30 13:48:46 - root - INFO - 刷新表格完成，总行数: 82
2025-06-30 13:48:46 - root - INFO - 成功获取变更单内容，共 82 条记录
2025-06-30 13:49:00 - root - DEBUG - 切换到标签页: 变更单管理
2025-06-30 13:49:00 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:49:01 - root - DEBUG - 切换到标签页: 变更单管理
2025-06-30 13:49:08 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:49:29 - root - DEBUG - 切换到标签页: 变更单管理
2025-06-30 13:49:29 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:52:50 - root - INFO - 应用程序正常退出
2025-06-30 13:54:00 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250630_ecn_cost.log
2025-06-30 13:54:02 - root - INFO - 数据管理器初始化完成
2025-06-30 13:54:02 - root - INFO - Application starting...
2025-06-30 13:54:02 - root - INFO - 成功加载样式表。
2025-06-30 13:54:03 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-06-30 13:54:03 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-06-30 13:54:03 - root - DEBUG - 初始化带勾选框的筛选表头
2025-06-30 13:54:03 - root - DEBUG - 安排了表头的延迟更新
2025-06-30 13:54:03 - root - DEBUG - 已设置表格初始列宽
2025-06-30 13:54:03 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-06-30 13:54:03 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-06-30 13:54:03 - root - INFO - 主窗口初始化完成
2025-06-30 13:54:03 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-06-30 13:54:32 - root - INFO - 应用程序正常退出
2025-06-30 13:54:51 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250630_ecn_cost.log
2025-06-30 13:54:52 - root - INFO - 数据管理器初始化完成
2025-06-30 13:54:52 - root - INFO - Application starting...
2025-06-30 13:54:52 - root - INFO - 成功加载样式表。
2025-06-30 13:54:53 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-06-30 13:54:53 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-06-30 13:54:53 - root - DEBUG - 初始化带勾选框的筛选表头
2025-06-30 13:54:53 - root - DEBUG - 安排了表头的延迟更新
2025-06-30 13:54:53 - root - DEBUG - 已设置表格初始列宽
2025-06-30 13:54:53 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-06-30 13:54:53 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-06-30 13:54:53 - root - INFO - 主窗口初始化完成
2025-06-30 13:54:53 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-06-30 13:54:58 - root - DEBUG - 切换到标签页: 变更单内容
2025-06-30 13:55:11 - root - DEBUG - 切换到标签页: 变更单管理
2025-06-30 13:55:17 - root - INFO - 应用程序正常退出
2025-06-30 14:11:36 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250630_ecn_cost.log
2025-06-30 14:11:36 - root - INFO - 数据管理器初始化完成
2025-06-30 14:11:36 - root - INFO - Application starting...
2025-06-30 14:11:36 - root - INFO - 成功加载样式表。
2025-06-30 14:11:36 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-06-30 14:11:36 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-06-30 14:11:36 - root - DEBUG - 初始化带勾选框的筛选表头
2025-06-30 14:11:36 - root - DEBUG - 安排了表头的延迟更新
2025-06-30 14:11:36 - root - DEBUG - 已设置表格初始列宽
2025-06-30 14:11:36 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-06-30 14:11:36 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-06-30 14:11:36 - root - INFO - 主窗口初始化完成
2025-06-30 14:11:36 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-06-30 14:11:45 - root - INFO - 应用程序正常退出
