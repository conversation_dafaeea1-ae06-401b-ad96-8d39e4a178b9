#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
后台任务模块，包含通用的Worker类和任务执行器
"""

from PyQt6.QtCore import QObject, pyqtSignal, QRunnable, QThreadPool
from typing import Callable, Any
from utils.logger import debug, error

class WorkerSignals(QObject):
    """
    定义Worker可发出的信号
    """
    finished = pyqtSignal(object)  # 任务完成信号，object是返回值
    error = pyqtSignal(str)        # 任务出错信号，str是错误信息
    progress = pyqtSignal(int)     # 任务进度信号，int是百分比

class Worker(QRunnable):
    """
    通用后台工作线程
    """
    def __init__(self, fn: Callable, *args: Any, **kwargs: Any):
        """
        初始化Worker
        
        Args:
            fn: 要在后台执行的函数
            *args: 函数的位置参数
            **kwargs: 函数的关键字参数
        """
        super().__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()

    def run(self):
        """执行任务"""
        try:
            debug(f"后台任务开始: {self.fn.__name__}")
            # 将进度回调函数注入到kwargs中
            self.kwargs['update_progress'] = self.signals.progress.emit
            result = self.fn(*self.args, **self.kwargs)
            self.signals.finished.emit(result)
            debug(f"后台任务完成: {self.fn.__name__}")
        except Exception as e:
            error_msg = f"后台任务 '{self.fn.__name__}' 执行出错: {str(e)}"
            error(error_msg)
            import traceback
            error(traceback.format_exc())
            self.signals.error.emit(error_msg) 