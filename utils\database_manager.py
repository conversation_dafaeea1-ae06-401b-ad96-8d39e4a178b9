#!/usr/bin/env python
# -*- coding: utf-8 -*-

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库管理模块，用于连接和查询SQL Server数据库以获取零件单价
"""

import pyodbc
from typing import Optional
import config
from utils.logger import info, error, debug

class DatabaseManager:
    """数据库管理类，用于连接数据库和执行查询"""

    def __init__(self):
        """初始化数据库管理器"""
        self.config = config.DB_CONFIG
        self.tables = config.DB_TABLES
        self.connection = None
        self.cursor = None
        debug("DatabaseManager instance created.")

    def connect(self) -> bool:
        """
        建立数据库连接
        
        Returns:
            True 如果连接成功，否则 False
        """
        try:
            # 为SQL Server构建连接字符串
            conn_str = (
                f"DRIVER={self.config['driver']};"
                f"SERVER={self.config['server']},{self.config['port']};"
                f"DATABASE={self.config['database']};"
                f"UID={self.config['user']};"
                f"PWD={self.config['password']}"
            )
            self.connection = pyodbc.connect(conn_str)
            info("SQL Server 数据库连接成功")
            return True
        except pyodbc.Error as e:
            error(f"SQL Server 数据库连接失败: {e}")
            self.connection = None
            return False

    def disconnect(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            info("数据库连接已关闭")

    def fetch_unit_price(self, part_code: str, is_repair: bool) -> Optional[str]:
        """
        根据零件编码和是否为返修件从数据库获取含税单价。
        此方法是线程安全的，它会自己管理数据库连接的生命周期。

        Args:
            part_code: 零件编码
            is_repair: 如果为True，则查询返修费表，否则查询普通物料表

        Returns:
            以字符串形式返回单价，如果未找到则返回None
        """
        if not part_code:
            return None
        
        # 建立此线程专属的连接
        if not self.connect():
            return None

        price = None
        try:
            with self.connection.cursor() as cursor:
                if is_repair:
                    # 查询返修费表 - 注意：对于返修项，零件编码对应的是"返修费备注"字段
                    table_name = self.tables['repair_fee']
                    debug(f"正在从返修表 {table_name} 查询零件 '{part_code}' 的单价")
                    
                    # 根据需求4.1-4.3，使用返修费备注字段进行匹配，并按ID降序获取单价
                    sql = f"SELECT TOP 1 [含税单价] FROM [{table_name}] WHERE [返修费备注] = ? ORDER BY [ID] DESC"
                else:
                    # 查询普通物料表
                    table_name = self.tables['ordinary_materials']
                    debug(f"正在从物料表 {table_name} 查询零件 '{part_code}' 的单价")
                    
                    # 根据需求3.1-3.3，使用料号字段进行匹配，并按ID降序获取单价
                    sql = f"SELECT TOP 1 [含税单价] FROM [{table_name}] WHERE [料号] = ? ORDER BY [ID] DESC"
                
                cursor.execute(sql, part_code)
                result = cursor.fetchone()

                if result:
                    price_value = result[0]
                    price = f"{float(price_value):.2f}"
                    debug(f"查询到零件 '{part_code}' 的单价为: {price}")
                else:
                    debug(f"在表 {table_name} 中未找到零件 '{part_code}' 的单价")

        except pyodbc.Error as e:
            error(f"查询零件 '{part_code}' 单价时出错: {e}")
        finally:
            # 确保此线程的连接被关闭
            self.disconnect()
        
        return price