#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
程序入口文件，负责初始化应用程序和加载主界面
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
import config
from views.main_window import MainWindow
import utils.logger  # 导入此模块以确保日志记录器被配置
import logging

def create_dirs():
    """创建必要的目录"""
    try:
        # 创建日志目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            logging.debug(f"创建目录: {log_dir}")
        
        # 创建资源目录
        icon_dir = config.ICON_FOLDER
        if not os.path.exists(icon_dir):
            os.makedirs(icon_dir)
            logging.debug(f"创建目录: {icon_dir}")
            
    except Exception as e:
        logging.error(f"创建目录时出错: {str(e)}")

def resource_path(relative_path):
    """ 获取资源的绝对路径，兼容开发环境和 PyInstaller 打包环境 """
    try:
        # PyInstaller 创建一个临时文件夹，并将其路径存储在 _MEIPASS 中
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

def main():
    """程序主函数"""
    try:
        # 创建必要的目录
        create_dirs()
        
        # 日志系统在导入 utils.logger 时已经被初始化，我们可以在这里记录应用启动
        logging.info("Application starting...")
        
        # 初始化应用程序
        app = QApplication(sys.argv)
        app.setApplicationName(config.APP_NAME)
        app.setApplicationVersion(config.APP_VERSION)
        
        # 加载样式表
        try:
            style_sheet_path = resource_path("resources/style.qss")
            with open(style_sheet_path, "r", encoding="utf-8") as f:
                app.setStyleSheet(f.read())
            logging.info("成功加载样式表。")
        except FileNotFoundError:
            logging.warning("未找到样式表 'resources/style.qss'。将使用默认样式运行。")
        except Exception as e:
            logging.error(f"加载样式表时出错: {e}")
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        logging.info(f"应用程序启动成功: {config.APP_NAME} v{config.APP_VERSION}")
        
        # 运行应用程序主循环
        return app.exec()
        
    except Exception as e:
        logging.error(f"应用程序启动失败: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 