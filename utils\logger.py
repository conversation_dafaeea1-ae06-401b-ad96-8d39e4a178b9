#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志工具模块，提供应用程序的日志记录功能
"""

import logging
import os
import sys
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
import config

# 这段代码在模块被导入时执行一次，用于配置全局日志系统

# 1. 判断日志文件的存放目录
app_name = "ECN_CostManage"
# `sys.frozen` 会被 PyInstaller 设置，用来判断程序是否被打包
if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
    # 如果是打包后的程序，将日志存放在用户 AppData 目录
    log_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser("~")), app_name, "logs")
else:
    # 如果是开发环境，存放在本地 logs 目录
    log_dir = "logs"

# 2. 如果目录不存在，则创建它
os.makedirs(log_dir, exist_ok=True)

# 3. 配置日志记录器
log_file_name = f"{datetime.now().strftime('%Y%m%d')}_ecn_cost.log"
log_file_path = os.path.join(log_dir, log_file_name)

logger = logging.getLogger()
# 避免重复加载时重复添加 handler
if not logger.handlers:
    logger.setLevel(logging.DEBUG)

    # 文件处理器 (TimedRotatingFileHandler 每天创建一个新日志文件)
    fh = TimedRotatingFileHandler(
        log_file_path, when="midnight", interval=1, backupCount=7, encoding="utf-8"
    )
    fh.setLevel(logging.DEBUG)
    
    # 控制台处理器
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    
    # 定义日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)

    logger.addHandler(fh)
    logger.addHandler(ch)

# 可以在导入时打印一条信息，方便调试
logging.info(f"日志系统已配置。日志文件位于: {log_file_path}")

class Logger:
    """日志记录器类，用于应用程序中的日志记录"""
    
    _instance = None
    
    def __new__(cls):
        """单例模式实现，确保全局只有一个日志实例"""
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance._setup_logger()
        return cls._instance
    
    def _setup_logger(self):
        """设置日志记录器"""
        # 获取配置
        log_level = getattr(logging, config.LOG_LEVEL)
        log_format = config.LOG_FORMAT
        log_date_format = config.LOG_DATE_FORMAT
        
        # 创建日志目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 生成日志文件名（加入日期）
        date_str = datetime.now().strftime("%Y%m%d")
        log_file = os.path.join(log_dir, f"{date_str}_{config.LOG_FILE}")
        
        # 配置根日志记录器
        self.logger = logging.getLogger()
        self.logger.setLevel(log_level)
        
        # 清除已存在的处理器
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_formatter = logging.Formatter(log_format, log_date_format)
        console_handler.setFormatter(console_formatter)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_formatter = logging.Formatter(log_format, log_date_format)
        file_handler.setFormatter(file_formatter)
        
        # 添加处理器到记录器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        
        # 记录初始化信息
        self.info(f"日志系统初始化完成，级别: {config.LOG_LEVEL}, 日志文件: {log_file}")
    
    def debug(self, message):
        """记录调试级别日志"""
        self.logger.debug(message)
    
    def info(self, message):
        """记录信息级别日志"""
        self.logger.info(message)
    
    def warning(self, message):
        """记录警告级别日志"""
        self.logger.warning(message)
    
    def error(self, message):
        """记录错误级别日志"""
        self.logger.error(message)
    
    def critical(self, message):
        """记录严重错误级别日志"""
        self.logger.critical(message)

# 创建全局日志实例
logger = Logger()

# 导出便捷函数
debug = logger.debug
info = logger.info
warning = logger.warning
error = logger.error
critical = logger.critical 