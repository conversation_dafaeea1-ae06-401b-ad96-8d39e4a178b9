# ECN成本管理系统

ECN成本管理系统是一个用于管理工程变更单(ECN)的应用程序，主要功能包括变更单文件的导入、内容解析、数据展示和筛选、成本计算等。

## 功能特点

1. **变更单管理功能**
   - 添加外部变更单Excel表格文件
   - 支持多次添加和一次选择多个文件
   - 防止重复添加相同的变更单
   - 可以选择删除已添加的变更单

2. **变更单内容解析**
   - 自动解析变更单Excel文件的固定格式内容
   - 提取关键信息如ERP号、设备名称、项目信息等
   - 解析变更零件信息

3. **变更单内容管理**
   - 表格化展示所有变更单的零件信息
   - 支持按各种条件筛选数据
   - 支持分页显示大量数据
   - 可以编辑零件单价
   - 自动计算总价和选中条目总价

## 技术栈

- Python 3.8+
- PyQt6 用户界面框架
- pandas, openpyxl 用于Excel文件处理
- 自定义模块化架构

## 安装与使用

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
python main.py
```

## 使用方法

1. **变更单管理**
   - 点击"添加变更单"按钮选择Excel文件
   - 选中表格中的变更单后，可以点击"删除选中"按钮删除

2. **变更单内容管理**
   - 切换到"变更单内容"标签页
   - 点击"变更内容获取"按钮从已添加的变更单中提取内容
   - 点击表头可以进行数据筛选
   - 点击单价列可以编辑价格，金额会自动计算
   - 页面底部有分页控件可以浏览所有数据

## 项目目录结构

```
ECN_CostManage/
├── main.py                    # 程序入口点
├── config.py                  # 全局配置文件
├── requirements.txt           # 依赖包列表
├── README.md                  # 项目说明文档
│
├── views/                     # 界面层
│   ├── __init__.py
│   ├── main_window.py         # 主窗口
│   ├── ecn_management.py      # 变更单管理页面
│   ├── ecn_content.py         # 变更单内容页面
│   └── custom_widgets.py      # 自定义控件
│
├── models/                    # 数据模型层
│   ├── __init__.py
│   ├── ecn_document.py        # 变更单文档类
│   ├── ecn_item.py            # 变更单条目类
│   └── data_manager.py        # 数据管理类
│
├── utils/                     # 工具层
│   ├── __init__.py
│   ├── excel_parser.py        # Excel解析工具
│   ├── file_manager.py        # 文件管理工具
│   ├── logger.py              # 日志工具
│   └── calculation.py         # 计算工具
│
├── resources/                 # 资源层
│   ├── Styles.qss            # 界面样式文件
│   └── icons/                # 图标资源
│
└── logs/                      # 日志文件夹(运行时创建)
```

## 注意事项

1. 程序期望解析的Excel文件需要有特定格式，详见变更单模板。
2. 使用前请确保所需的依赖库已正确安装。
3. 日志文件会自动保存在logs目录下，可用于排查问题。

## 许可证

本项目为内部使用软件，版权所有，未经授权不得外传或用于商业用途。 