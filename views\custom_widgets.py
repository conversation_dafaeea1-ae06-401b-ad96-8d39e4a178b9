#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自定义控件模块，提供应用程序中使用的自定义UI组件
"""

from PyQt6.QtCore import Qt, pyqtSignal, QEvent, QPoint, QSize, QRect, QSortFilterProxyModel, QModelIndex
from PyQt6.QtWidgets import (
    QWidget, QTableWidget, QTableWidgetItem, QHeaderView, QCheckBox, 
    QComboBox, QStyledItemDelegate, QMenu, QPushButton, QLabel, 
    QHBoxLayout, QVBoxLayout, QDialog, QLineEdit, QScrollArea, QFrame,
    QDialogButtonBox
)
from PyQt6.QtGui import QAction, QFont, QColor, QPen, QPolygon, QBrush, QCursor, QMouseEvent, QStandardItemModel, QStandardItem, QPainter
from typing import Dict, List, Any, Set, Optional, Callable, Tuple
import config
from utils.logger import info, debug, error

class CheckableHeaderView(QHeaderView):
    """
    可勾选的表格头视图，允许在表头中添加复选框
    """
    
    checkboxClicked = pyqtSignal(int, bool)  # 信号：复选框状态改变
    
    def __init__(self, orientation=Qt.Orientation.Horizontal, parent=None):
        """
        初始化表头视图
        
        Args:
            orientation: 方向，水平或垂直
            parent: 父部件
        """
        super().__init__(orientation, parent)
        self._checkable_columns = []  # 可勾选的列索引列表
        self._checked_columns = {}     # 已勾选的列状态字典
        
        # 启用自定义绘制和点击事件
        self.setSectionsClickable(True)
        self.sectionClicked.connect(self._on_section_clicked)
        
        # 设置表头样式
        self.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        self.setStretchLastSection(True)
        self.setHighlightSections(True)
    
    def set_checkable(self, column: int, checkable: bool = True):
        """
        设置列头是否可勾选
        
        Args:
            column: 列索引
            checkable: 是否可勾选
        """
        if checkable:
            if column not in self._checkable_columns:
                self._checkable_columns.append(column)
                self._checked_columns[column] = False
        else:
            if column in self._checkable_columns:
                self._checkable_columns.remove(column)
                if column in self._checked_columns:
                    del self._checked_columns[column]
    
    def is_checked(self, column: int) -> bool:
        """
        获取列头复选框是否选中
        
        Args:
            column: 列索引
            
        Returns:
            是否选中
        """
        return self._checked_columns.get(column, False)
    
    def set_checked(self, column: int, checked: bool):
        """
        设置列头复选框状态
        
        Args:
            column: 列索引
            checked: 是否选中
        """
        if column in self._checkable_columns:
            self._checked_columns[column] = checked
            self.updateSection(column)
            self.checkboxClicked.emit(column, checked)
    
    def _on_section_clicked(self, logical_index):
        """
        处理表头点击事件
        
        Args:
            logical_index: 逻辑列索引
        """
        if logical_index in self._checkable_columns:
            checked = not self._checked_columns.get(logical_index, False)
            self._checked_columns[logical_index] = checked
            self.updateSection(logical_index)
            self.checkboxClicked.emit(logical_index, checked)


class FilterDialog(QDialog):
    """
    筛选对话框，显示筛选选项列表和搜索框
    """
    
    def __init__(self, values: List[Any], 
                 current_filters: Optional[List[Any]] = None, 
                 value_statistics: Optional[Dict[Any, Dict[str, int]]] = None,
                 parent=None):
        """
        初始化筛选对话框
        
        Args:
            values: 所有可选值列表
            current_filters: 当前已选择的筛选值列表，如果为None则表示全选
            value_statistics: 值统计信息
            parent: 父部件
        """
        super().__init__(parent)
        self.values = values
        self.current_filters = current_filters if current_filters is not None else list(values)
        self.result_filters = self.current_filters.copy()
        self.value_statistics = value_statistics if value_statistics is not None else {}
        self.checkboxes: List[Tuple[Any, QCheckBox]] = []
        
        debug(f"FilterDialog.__init__: Received values (all possible options): {len(self.values)} items.")
        debug(f"FilterDialog.__init__: Received current_filters (initial selection for result_filters): {self.current_filters} ({len(self.current_filters)} items).")
        debug(f"FilterDialog.__init__: Received value_statistics: {self.value_statistics}")
        debug(f"FilterDialog.__init__: self.result_filters initialized to: {self.result_filters} ({len(self.result_filters)} items).")
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("筛选")
        self.setMinimumWidth(200)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.MSWindowsFixedSizeDialogHint)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索...")
        self.search_input.textChanged.connect(self._on_search_changed)
        main_layout.addWidget(self.search_input)
        
        # 全选勾选框
        self.select_all_checkbox = QCheckBox("全选")
        self.select_all_checkbox.setTristate(True)
        self.select_all_checkbox.stateChanged.connect(self._on_select_all_changed)
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.StyledPanel)
        scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(scroll_content)
        self.scroll_layout.setContentsMargins(5, 5, 5, 5)
        self.scroll_layout.setSpacing(5)
        
        for value in self.values:
            # 如果值为空字符串，则显示为(空白)，以方便用户理解
            display_text = str(value) if str(value).strip() != "" else "(空白)"
            checkbox = QCheckBox(display_text)

            # 根据当前筛选条件设置初始勾选状态
            if value in self.current_filters:
                checkbox.setCheckState(Qt.CheckState.Checked)
            else:
                checkbox.setCheckState(Qt.CheckState.Unchecked)

            # 确保单个选项的复选框是二态的（勾选/未勾选），而不是三态
            checkbox.setTristate(False)
            checkbox.stateChanged.connect(lambda state, v=value: self._on_item_changed(v, state))
            self.checkboxes.append((value, checkbox))
            self.scroll_layout.addWidget(checkbox)

        # 根据各选项的初始状态，更新"全选"复选框的初始状态
        self._update_select_all_state()

        self.scroll_layout.addStretch()
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(self.select_all_checkbox)
        main_layout.addWidget(scroll_area)
        
        # 确定和取消按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.button(QDialogButtonBox.StandardButton.Ok).setText("确定")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("取消")
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
    
    def _on_search_changed(self, text):
        """
        搜索框内容变化事件
        
        Args:
            text: 搜索文本
        """
        # 断开信号，防止循环触发
        for value, cb in self.checkboxes:
            cb.stateChanged.disconnect()
        
        # 根据搜索文本过滤复选框
        for value, cb in self.checkboxes:
            cb.setVisible(text.lower() in str(value).lower())
        
        # 搜索后更新"全选"状态
        self._update_select_all_state()
    
    def _on_select_all_changed(self, state):
        """处理"全选"复选框的状态变化。"""
        # 如果是程序设置的部分选中状态，则忽略，避免循环
        if state == Qt.CheckState.PartiallyChecked:
            return

        # 确定目标状态
        should_check_all = (state == Qt.CheckState.Checked)

        # 批量更新，先阻断信号避免事件风暴
        for _, cb in self.checkboxes:
            cb.blockSignals(True)
        
        # 更新所有可见的复选框及底层数据模型
        for value, cb in self.checkboxes:
            if cb.isVisible():
                cb.setChecked(should_check_all)
                if should_check_all:
                    if value not in self.result_filters:
                        self.result_filters.append(value)
                else:
                    if value in self.result_filters:
                        self.result_filters.remove(value)
        
        # 恢复所有信号
        for _, cb in self.checkboxes:
            cb.blockSignals(False)

    def _update_select_all_state(self):
        """根据当前可见项的勾选状态，更新"全选"复选框的状态。"""
        # 断开信号，防止在程序化设置状态时触发事件
        self.select_all_checkbox.stateChanged.disconnect()

        checked_count = 0
        visible_count = 0
        
        # 统计可见复选框中的勾选数量，但依赖更可靠的数据模型
        for value, checkbox in self.checkboxes:
            if checkbox.isVisible():
                visible_count += 1
                # 不再使用 isChecked()，而是检查值是否在我们的结果列表中
                if value in self.result_filters:
                    checked_count += 1
        
        # 根据统计结果设置"全选"复选框的状态
        if visible_count > 0 and checked_count == visible_count:
            self.select_all_checkbox.setCheckState(Qt.CheckState.Checked)
        elif checked_count > 0:
            self.select_all_checkbox.setCheckState(Qt.CheckState.PartiallyChecked)
        else:
            self.select_all_checkbox.setCheckState(Qt.CheckState.Unchecked)

        # 重新连接信号
        self.select_all_checkbox.stateChanged.connect(self._on_select_all_changed)

    def _on_item_changed(self, value, state):
        """
        单个项目状态变化事件
        
        Args:
            value: 项目值
            state: 勾选状态 (int: 0=Unchecked, 1=PartiallyChecked, 2=Checked)
        """
        # state comes from QCheckBox.stateChanged(int)
        # Qt.CheckState.Unchecked.value = 0, Qt.CheckState.PartiallyChecked.value = 1, Qt.CheckState.Checked.value = 2
        is_item_now_checked = (state == Qt.CheckState.Checked.value)
        is_item_now_unchecked = (state == Qt.CheckState.Unchecked.value)
        # If a tristate checkbox is clicked by user, it cycles Unchecked -> Checked -> (if tristate) PartiallyChecked -> Unchecked...
        # Or Unchecked -> Checked -> Unchecked if not tristate.
        # We will simplify: if user clicks a partially checked box, it becomes fully CHECKED.
        # If user clicks a fully checked box, it becomes UNCHECKED.
        # If user clicks an unchecked box, it becomes CHECKED.

        debug(f"FilterDialog: _on_item_changed for value '{str(value)}'. Qt state from signal={state}")

        current_check_state_of_item = Qt.CheckState.Unchecked
        for v, cb in self.checkboxes:
            if v == value:
                current_check_state_of_item = cb.checkState()
                break
        
        # Effective new state based on simplified user interaction:
        # Clicking a partially checked item makes it fully checked.
        # Clicking a fully checked item makes it unchecked.
        # Clicking an unchecked item makes it fully checked.
        
        # This logic is for updating result_filters.
        # The checkbox itself will have its state due to user click. We need to sync result_filters.
        # If an item is now checked (state == 2) or partially checked (state == 1), consider it for inclusion.
        # If an item is unchecked (state == 0), remove it.

        if state == Qt.CheckState.Checked.value or state == Qt.CheckState.PartiallyChecked.value : # Checked or Partially
            if value not in self.result_filters:
                self.result_filters.append(value)
                debug(f"FilterDialog: Added '{str(value)}' to result_filters. New count: {len(self.result_filters)}")
        else: # Unchecked
            if value in self.result_filters:
                try:
                    self.result_filters.remove(value)
                    debug(f"FilterDialog: Removed '{str(value)}' from result_filters. New count: {len(self.result_filters)}")
                except ValueError:
                    debug(f"FilterDialog: WARNING - Value '{str(value)}' not found in result_filters for removal.")
        
        debug(f"FilterDialog: AFTER item change: self.result_filters ({len(self.result_filters)} items) = {self.result_filters}")

        # 更新 "全选" 勾选框状态
        self._update_select_all_state()
    
    def get_filters(self):
        """
        获取最终选择的筛选值列表
        
        Returns:
            筛选值列表
        """
        debug(f"FilterDialog: get_filters returning {len(self.result_filters)} filters from: {self.result_filters}")
        return self.result_filters.copy()


class FilterHeader(QHeaderView):
    """
    筛选表头，提供表格列筛选功能
    """
    
    filterChanged = pyqtSignal(int, list)  # 信号：筛选条件改变
    
    def __init__(self, orientation=Qt.Orientation.Horizontal, parent=None):
        """
        初始化筛选表头
        
        Args:
            orientation: 方向，水平或垂直
            parent: 父部件
        """
        super().__init__(orientation, parent)
        self._filters = {}  # 列筛选条件字典: {列索引: [筛选值列表]}
        self._disabled_columns = []  # 禁用筛选的列
        
        # 设置表头样式和交互
        self.setSectionsClickable(True)
        self.sectionClicked.connect(self._on_section_clicked)
        self.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        self.setStretchLastSection(True)
        self.setHighlightSections(True)
        
        # 过滤器图标位置的矩形区域
        self._filter_rects = {}
    
    def disable_filter_for_column(self, column):
        """
        禁用指定列的筛选功能
        
        Args:
            column: 列索引
        """
        if column not in self._disabled_columns:
            self._disabled_columns.append(column)
    
    def paintSection(self, painter, rect, logicalIndex):
        """重写绘制方法，添加过滤图标"""
        # 保存绘制状态
        painter.save()
        
        # 调用基类方法绘制表头
        super().paintSection(painter, rect, logicalIndex)
        
        # 如果不是禁用的列，则绘制过滤图标
        if logicalIndex not in self._disabled_columns:
            # 在表头右边添加过滤图标
            icon_size = 16
            filter_rect = QRect(
                rect.right() - icon_size - 4,
                rect.top() + (rect.height() - icon_size) // 2,
                icon_size,
                icon_size
            )
            
            # 保存图标区域，用于点击检测
            self._filter_rects[logicalIndex] = filter_rect
            
            # Determine if the column is actively filtered for icon display
            is_column_filtered = False
            if logicalIndex in self._filters:
                # A column is considered filtered if its filter entry exists AND it's not selecting all possible values.
                # This requires knowing all_possible_values, which is tricky here.
                # Simpler: if self._filters[logicalIndex] is not selecting all unique values present in the table for that column.
                # For now, presence of key in self._filters implies it's actively filtered for icon purpose.
                # A more robust check would compare len(self._filters[logicalIndex]) with len(all_unique_values_for_column)
                if self._filters[logicalIndex] is not None: # Assuming None means "no filter" or "all selected"
                     # To be more precise, we need to know if the filter list is a SUBSET of all possible values
                     # For icon display, if a filter list exists and is not empty, consider it "filtered"
                     if self._filters[logicalIndex]: # If list is not empty
                         is_column_filtered = True

            if is_column_filtered:
                painter.setBrush(QColor("#4A86E8")) # Blue brush if filtered
                icon_color = QColor("#FFFFFF") # White icon
            else:
                painter.setBrush(QColor("transparent")) # Transparent brush if not filtered
                icon_color = QColor("#666666") # Dark gray icon
            
            painter.setPen(QPen(icon_color))
            x = filter_rect.x()
            y = filter_rect.y()
            w = filter_rect.width()
            h = filter_rect.height()
            
            # 画漏斗形状
            points = [
                QPoint(x + 2, y + 4),           # 左上
                QPoint(x + w - 2, y + 4),       # 右上
                QPoint(x + w * 2//3, y + h * 2//3), # 右中
                QPoint(x + w * 2//3, y + h - 4),    # 右下
                QPoint(x + w * 1//3, y + h - 4),    # 左下
                QPoint(x + w * 1//3, y + h * 2//3), # 左中
                QPoint(x + 2, y + 4)            # 回到左上
            ]
            painter.drawPolygon(points)
        
        # 恢复绘制状态
        painter.restore()
    
    def _on_section_clicked(self, logical_index):
        """
        处理表头点击事件，显示筛选对话框
        
        Args:
            logical_index: 逻辑列索引
        """
        # 如果是禁用筛选的列，直接返回
        if logical_index in self._disabled_columns:
            return
        
        # Get mouse position relative to the header
        header_local_pos = self.mapFromGlobal(QCursor.pos())
        
        filter_icon_rect = self._filter_rects.get(logical_index)

        # Only open dialog if the click was on the filter icon
        if not filter_icon_rect or not filter_icon_rect.contains(header_local_pos):
            # If the click was not on the icon, do not open the filter dialog.
            # Allow base class to handle for sorting or other section click behaviors if implemented.
            # We explicitly do not call super().mousePressEvent here to prevent unintended effects
            # if the goal is only to filter via icon.
            # If sorting on header text click is desired, that needs separate handling.
            debug(f"FilterHeader: Click on section {logical_index} text area, not icon. No filter dialog.")
            return # Explicitly do nothing further for filter dialog opening

        model = self.model()
        # Check if a model is associated with the table view this header is part of
        if not model:
            debug(f"FilterHeader: No model found for column {logical_index}. Cannot filter.")
            return
        
        # Try to get the ECNContentPage instance to call get_all_column_values
        # This assumes a specific parent structure: QTableWidget -> ECNContentPage
        parent_table = self.parentWidget()
        page_widget = None
        if parent_table and hasattr(parent_table, 'parentWidget'):
            page_widget = parent_table.parentWidget()

        all_possible_values = []
        if page_widget and hasattr(page_widget, 'get_all_column_values'):
            try:
                all_possible_values = page_widget.get_all_column_values(logical_index)
                debug(f"FilterHeader: Fetched {len(all_possible_values)} total unique values for column {logical_index} via ECNContentPage.get_all_column_values.")
            except Exception as e:
                error(f"FilterHeader: Error calling get_all_column_values for column {logical_index}: {str(e)}. Falling back.")
                all_possible_values = self._get_values_from_current_view(logical_index) # Fallback
        else:
            debug(f"FilterHeader: Could not find get_all_column_values method. Falling back for column {logical_index}.")
            all_possible_values = self._get_values_from_current_view(logical_index)
            
        if not all_possible_values:
            debug(f"FilterHeader: No values found for column {logical_index} to filter by (empty list from get_all_column_values).")
            return

        # Get statistics for each value in the column (total vs displayed)
        value_statistics_for_dialog: Dict[Any, Dict[str, int]] = {}
        if page_widget and hasattr(page_widget, 'get_value_statistics_for_column'):
            try:
                value_statistics_for_dialog = page_widget.get_value_statistics_for_column(logical_index)
                debug(f"FilterHeader: For col {logical_index}, fetched value statistics: {value_statistics_for_dialog}")
            except Exception as e:
                error(f"FilterHeader: Error calling get_value_statistics_for_column for col {logical_index}: {str(e)}")
        else:
            debug(f"FilterHeader: Could not find get_value_statistics_for_column method for col {logical_index}.")

        # initially_checked_values_in_dialog: these are the values that are currently DISPLAYED.
        # This list is used by FilterDialog to determine which *items in its list* (all_possible_values)
        # should contribute to the initial `result_filters`.
        # The actual check state of each checkbox in dialog now comes from value_statistics.
        initially_displayed_values = []
        if page_widget and hasattr(page_widget, 'get_currently_displayed_unique_values'):
            try:
                initially_displayed_values = page_widget.get_currently_displayed_unique_values(logical_index)
            except Exception as e:
                error(f"FilterHeader: Error calling get_currently_displayed_unique_values for col {logical_index}: {str(e)}")
                initially_displayed_values = list(all_possible_values) # Fallback
        else:
            initially_displayed_values = list(all_possible_values) # Fallback
        
        # Pass all_possible_values for the dialog to list,
        # initially_displayed_values to guide which of those are considered "selected" for result_filters,
        # and value_statistics to set the tri-state of each checkbox.
        dialog = FilterDialog(all_possible_values, 
                              initially_displayed_values, 
                              value_statistics_for_dialog, 
                              self)
        
        table_model = self.model()
        dialog_title_text = "筛选"
        if table_model: # Check if model exists before querying headerData
            header_text = table_model.headerData(logical_index, Qt.Orientation.Horizontal, Qt.ItemDataRole.DisplayRole)
            if header_text:
                dialog_title_text = f"筛选 - {header_text}"
        dialog.setWindowTitle(dialog_title_text)
        
        # Position dialog below the clicked header section
        section_visual_x = self.sectionViewportPosition(logical_index)
        header_global_pos = self.mapToGlobal(QPoint(section_visual_x, self.height()))
        dialog.move(header_global_pos)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_filters = dialog.get_filters()
            debug(f"FilterHeader: Dialog accepted for column {logical_index}. New filters count: {len(new_filters)}.")

            # Convert to sets of strings for robust comparison, handles mixed types.
            new_filters_str_set = set(map(str, new_filters))
            all_possible_values_str_set = set(map(str, all_possible_values))

            # If no items are selected OR all items are selected, effectively remove the filter for this column.
            if not new_filters_str_set or new_filters_str_set == all_possible_values_str_set:
                if logical_index in self._filters:
                    del self._filters[logical_index]
                    debug(f"FilterHeader: Filter removed for column {logical_index} (all/none selected).")
                # Emit a signal indicating all values, effectively clearing the specific filter for this column.
                self.filterChanged.emit(logical_index, list(all_possible_values)) 
            else:
                self._filters[logical_index] = new_filters
                debug(f"FilterHeader: Filter set for column {logical_index} with {len(new_filters)} items.")
                self.filterChanged.emit(logical_index, new_filters)
            
            self.viewport().update() # Request a repaint of the header to update filter icon
        else:
            debug(f"FilterHeader: Dialog cancelled for column {logical_index}.")
    
    def _get_values_from_current_view(self, logical_index):
        """
        从当前表格视图获取指定列的所有值
        
        Args:
            logical_index: 列索引
            
        Returns:
            值列表
        """
        values = []
        model = self.model()
        
        # 从当前表格视图获取值
        if model and model.rowCount() > 0:
            for row in range(model.rowCount()):
                item = model.data(model.index(row, logical_index))
                if item is not None and str(item) not in [str(v) for v in values]:
                    values.append(item)
                    
            # 排序所有值（字符串排序）
            values.sort(key=lambda x: str(x).lower())
            
        return values
    
    def get_filter(self, column):
        """
        获取指定列的筛选条件
        
        Args:
            column: 列索引
            
        Returns:
            筛选值列表
        """
        return self._filters.get(column)
    
    def get_all_filters(self):
        """
        获取所有列的筛选条件
        
        Returns:
            筛选条件字典: {列索引: [筛选值列表]}
        """
        return self._filters.copy()
    
    def clear_filters(self):
        """清空所有筛选条件"""
        if self._filters: # Check if there are any filters to clear
            self._filters.clear()
            debug("FilterHeader: All column filters cleared.")
            self.viewport().update() # Update header icons to reflect no filters
            # Note: This should also trigger a broader table refresh via a signal if needed,
            # e.g., by emitting filterChanged for all previously filtered columns with "all values".
            # For now, just clearing internal state and updating header visuals.
            # The main page should probably re-fetch/re-filter data when all filters are cleared globally.


class PaginationWidget(QWidget):
    """
    分页控件，提供表格分页功能
    """
    
    pageChanged = pyqtSignal(int)  # 信号：页码改变
    
    def __init__(self, parent=None):
        """
        初始化分页控件
        
        Args:
            parent: 父部件
        """
        super().__init__(parent)
        self._current_page = 1
        self._total_pages = 1
        self._rows_per_page = config.MAX_ROWS_PER_PAGE
        self._total_rows = 0
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 首页按钮
        self.first_page_btn = QPushButton("首页")
        self.first_page_btn.setObjectName("pageButton")
        self.first_page_btn.clicked.connect(self._goto_first_page)
        
        # 上一页按钮
        self.prev_page_btn = QPushButton("上一页")
        self.prev_page_btn.setObjectName("pageButton")
        self.prev_page_btn.clicked.connect(self._goto_prev_page)
        
        # 页码信息标签
        self.page_info_label = QLabel("第 1/1 页")
        self.page_info_label.setObjectName("pageInfoLabel")
        self.page_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.page_info_label.setMinimumWidth(100)
        
        # 下一页按钮
        self.next_page_btn = QPushButton("下一页")
        self.next_page_btn.setObjectName("pageButton")
        self.next_page_btn.clicked.connect(self._goto_next_page)
        
        # 末页按钮
        self.last_page_btn = QPushButton("末页")
        self.last_page_btn.setObjectName("pageButton")
        self.last_page_btn.clicked.connect(self._goto_last_page)
        
        # 添加控件到布局
        layout.addStretch(1)
        layout.addWidget(self.first_page_btn)
        layout.addWidget(self.prev_page_btn)
        layout.addWidget(self.page_info_label)
        layout.addWidget(self.next_page_btn)
        layout.addWidget(self.last_page_btn)
        layout.addStretch(1)
        
        self.setLayout(layout)
        self._update_ui_state()
    
    def _update_ui_state(self):
        """更新界面状态"""
        # 更新页码信息
        self.page_info_label.setText(f"第 {self._current_page}/{self._total_pages} 页")
        
        # 禁用/启用按钮
        self.first_page_btn.setEnabled(self._current_page > 1)
        self.prev_page_btn.setEnabled(self._current_page > 1)
        self.next_page_btn.setEnabled(self._current_page < self._total_pages)
        self.last_page_btn.setEnabled(self._current_page < self._total_pages)
    
    def _goto_first_page(self):
        """跳转到首页"""
        if self._current_page != 1:
            self._current_page = 1
            self._update_ui_state()
            self.pageChanged.emit(1)
    
    def _goto_prev_page(self):
        """跳转到上一页"""
        if self._current_page > 1:
            self._current_page -= 1
            self._update_ui_state()
            self.pageChanged.emit(self._current_page)
    
    def _goto_next_page(self):
        """跳转到下一页"""
        if self._current_page < self._total_pages:
            self._current_page += 1
            self._update_ui_state()
            self.pageChanged.emit(self._current_page)
    
    def _goto_last_page(self):
        """跳转到末页"""
        if self._current_page != self._total_pages:
            self._current_page = self._total_pages
            self._update_ui_state()
            self.pageChanged.emit(self._current_page)
    
    def set_total_rows(self, total_rows: int):
        """
        设置总行数，计算总页数
        
        Args:
            total_rows: 总行数
        """
        self._total_rows = max(0, total_rows)
        self._total_pages = max(1, (self._total_rows + self._rows_per_page - 1) // self._rows_per_page)
        
        current_pg = self.get_current_page()
        page_changed = False

        if current_pg > self._total_pages:
            self._current_page = self._total_pages
            page_changed = True
        # Ensure current page is at least 1 (should be guaranteed by _total_pages >= 1)
        # elif current_pg < 1:
        # self._current_page = 1
        # page_changed = True

        self._update_ui_state() # Update button states etc.
        if page_changed:
            self.pageChanged.emit(self._current_page) # Emit signal if page was adjusted
    
    def get_current_page(self) -> int:
        """
        获取当前页码
        
        Returns:
            当前页码
        """
        return self._current_page
    
    def get_total_pages(self) -> int:
        """
        获取总页数
        
        Returns:
            总页数
        """
        return self._total_pages
    
    def get_rows_per_page(self) -> int:
        """
        获取每页行数
        
        Returns:
            每页行数
        """
        return self._rows_per_page
    
    def reset(self):
        """重置为第一页"""
        self._current_page = 1
        self._update_ui_state()


class TotalPriceWidget(QWidget):
    """
    总价显示控件，显示总金额
    """
    
    def __init__(self, title: str = "总价", parent=None):
        """
        初始化总价显示控件
        
        Args:
            title: 标签标题
            parent: 父部件
        """
        super().__init__(parent)
        self._title = title
        self._price = "0.00"
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题标签
        title_label = QLabel(f"{self._title}:")
        title_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        
        # 价格标签
        self.price_label = QLabel(self._price)
        self.price_label.setObjectName("totalPriceLabel")
        self.price_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        
        # 添加控件到布局
        layout.addWidget(title_label)
        layout.addWidget(self.price_label)
        
        self.setLayout(layout)
    
    def set_price(self, price: str):
        """
        设置价格显示
        
        Args:
            price: 价格字符串
        """
        self._price = price
        self.price_label.setText(self._price)
    
    def get_price(self) -> str:
        """
        获取当前显示价格
        
        Returns:
            价格字符串
        """
        return self._price 