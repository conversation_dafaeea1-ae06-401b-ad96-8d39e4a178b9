# 变更单表格内容解析和提取功能
## 1.变更单模板说明：
    1.1 几个固定的单元格内容：
        单元格B2：当前变更单的所属ERP号；
        单元格D2：当前变更单的所属设备名称；
        单元格H2：当前变更单的所属项目编号；
        单元格K2：当前变更单的所属项目名称；
        单元格N2：当前变更单的变更单号；------之前是单元格P2，现在改为N2；
    1.2 需要识别和使用的几个固定表头：
        表头A3：序号
        表头B3：变更原因
        表头C3：零件编码
        表头D3：新版本
        表头E3：名称
        表头F3：材料
        表头G3：新版本实施内容
        表头H3：数量单台----（单台）
        表头I3：台数
        表头J3：总数量
        表头K3：涉及计划号
        表头L3：需求日期
        表头M3：问题分类
        表头N3：旧版本
        表头O3：已有零件处理建议
        表头O3会包含以下几个子表头：
           表头O4：在制在购
           表头P4：库存
           表头Q4：使用中
           表头R4：变更成本----删除
    1.3 以上的所有表头，占用了前行；所以，具体的表格内容是从第5行开始，所以需要从第5行开始，读取表格内容。结束行的判断依据为：当A列读取到某一行的内容为“部门人员”时，或者当A列读取到某一行的内容为空时，则该行的上一行就是最后一行内容，即表格结束行。-----之前的判断字据为“填写说明”

## 2.变更单内容UI界面创建：
    2.1 创建一个变更单内容管理页面，要包含以下表头：
    表头A：零件名称----对应所属变更单的表头E3列，也就是名称；
    表头B：零件编码----对应所属变更单的表头C3列，也就是零件编码；
    表头C：ERP号----对应所属变更单的单元格B2的内容，也就是ERP号；
    表头D：计划号----对应所属变更单的表头K3列，也就是涉及计划号；
    表头E：所属设备名称----对应所属变更单的单元格D2的内容，也就是设备名称；
    表头F：所属项目名称----对应所属变更单的单元格K2的内容，也就是项目名称；
    表头G：所属项目编号----对应所属变更单的单元格H2的内容，也就是项目编号；
    表头H：所属变更单单号----对应所属变更单的表头N2列，也就是变更单编号；-------之前是P2列，现在改为N2列；
    表头I：新版本实施内容----对应所属变更单的表头G3列，也就是新版本实施内容；
    表头J：变更原因----对应所属变更单的表头B3列，也就是变更原因；
    表头K：问题分类----对应所属变更单的表头M3列，也就是问题分类；
    表头L：工号----需要解读所属变更单单号的内容："ECN-"后面的数字就是工号；比如变更单单号位“ECN-22018-250109-04”，则工号为22018；
    表头M：变更日期----需要解读所属变更单单号的内容：对应工号后面的数字就是变更日期；比如变更单单号位“ECN-22018-250109-04”，则变更日期则为250109；
    表头N：数量----对应所属变更单的表头J3列，也就是总数量；
    表头O：已有零件处理意见-在制在购，对应所属变更单的表头O4列；
    表头P：已有零件处理意见-库存，对应所属变更单的表头P4列；
    表头Q：已有零件处理意见-使用中，对应所属变更单的表头Q4列；
    表头R：已有零件处理意见-变更成本，对应所属变更单的表头R4列；------直接取消这列
    表头R：单价----需要单独获取，具体的获取逻辑需要参考后面的描述；保留2位小数点，默认是0.00；
    表头S：金额----等于单价*数量，保留2位小数点，默认是0.00；
    2.2 每一行最左侧，都有一个勾选框，可以选中该行；第一行勾选框的上面，有一个全选框，可以选中所有筛选过滤之后的所有页面的行内容；
    2.3 每列具备筛选功能，可以进行筛选过滤；筛选小箭头放在每一列的右上角；
    2.4 表头的上方，具备以下几个控件元素：
        a.变更内容获取按钮：点击之后，会将变更单管理页面的变更单列表中的所有变更单的内容，提取到该页面中，并显示在表格中；提取的内容，就按照上述表头A-L的对应内容，一行一行提取到当前表格页面中；不在当前列表中的内容，要删除掉，只保留列表中存在的变更内容；
        b.总价显示控件：显示当前筛选过滤之后，所有的零件总价之和；也就是把过滤之后的所有L列中的数值，进行求和，并显示在控件中；保留2位小数点。
        c.选中行总价显示控件：显示当前选中行的零件总价；也就是把当前选中行的L列中的数值，进行求和，并显示在控件中；保留2位小数点。
        d.取消过滤按钮，点击之后，会取消所有列的过滤条件，将所有行内容全部显示在表格中；
    2.5 每一页最多显示100行数据，可以分多页显示；
    2.6 每一行零件的单价的获取逻辑，暂时不确定，可以先不处理；