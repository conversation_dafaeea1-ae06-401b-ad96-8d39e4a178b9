2025-07-02 18:32:55 - root - INFO - 日志系统初始化完成，级别: DEBUG, 日志文件: logs\20250702_ecn_cost.log
2025-07-02 18:32:56 - root - INFO - 数据管理器初始化完成
2025-07-02 18:32:56 - root - INFO - Application starting...
2025-07-02 18:32:57 - root - INFO - 成功加载样式表。
2025-07-02 18:32:58 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-07-02 18:32:58 - root - INFO - ECN管理页面初始化线程池，最大线程数: 16
2025-07-02 18:32:58 - root - DEBUG - 初始化带勾选框的筛选表头
2025-07-02 18:32:58 - root - DEBUG - 安排了表头的延迟更新
2025-07-02 18:32:58 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:32:58 - root - INFO - ECN内容页面初始化线程池，最大线程数: 16
2025-07-02 18:32:58 - root - ERROR - 样式表文件不存在: resources/Styles.qss
2025-07-02 18:32:58 - root - INFO - 主窗口初始化完成
2025-07-02 18:32:58 - root - INFO - 应用程序启动成功: ECN成本管理系统 v1.0.0
2025-07-02 18:33:03 - root - DEBUG - 切换到标签页: 变更单内容
2025-07-02 18:33:05 - root - DEBUG - 切换到标签页: 变更单管理
2025-07-02 18:37:01 - root - DEBUG - 后台任务开始: parse_files_in_background
2025-07-02 18:37:01 - root - INFO - 开始解析变更单文档: 工程变更单_20062_ECN-20023-250126-10.xlsx
2025-07-02 18:37:01 - root - INFO - 开始解析Excel文件: D:/桌面/工程变更单_20062_ECN-20023-250126-10.xlsx
2025-07-02 18:37:01 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-07-02 18:37:01 - PIL.PngImagePlugin - DEBUG - STREAM b'sRGB' 41 1
2025-07-02 18:37:01 - PIL.PngImagePlugin - DEBUG - STREAM b'gAMA' 54 4
2025-07-02 18:37:01 - PIL.PngImagePlugin - DEBUG - STREAM b'pHYs' 70 9
2025-07-02 18:37:01 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 91 3176
2025-07-02 18:37:01 - root - DEBUG - 提取基本信息: ERP号 = 20062
2025-07-02 18:37:01 - root - DEBUG - 提取基本信息: 设备名称 = Busbar切平
2025-07-02 18:37:01 - root - DEBUG - 提取基本信息: 项目编号 = CN23HA005
2025-07-02 18:37:01 - root - DEBUG - 提取基本信息: 项目名称 = 广汽锐湃IDU定子装配线
（第一条线）
2025-07-02 18:37:01 - root - DEBUG - 提取基本信息: 变更单号 = ECN-20023-250126-10
2025-07-02 18:37:01 - root - DEBUG - 提取的基本信息: {'ERP号': '20062', '设备名称': 'Busbar切平', '项目编号': 'CN23HA005', '项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '变更单号': 'ECN-20023-250126-10'}
2025-07-02 18:37:01 - root - DEBUG - 正在解析第 5 行数据
2025-07-02 18:37:01 - root - DEBUG - 第 5 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-07-02 18:37:01 - root - DEBUG - 正在解析第 6 行数据
2025-07-02 18:37:01 - root - DEBUG - 第 6 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-07-02 18:37:01 - root - DEBUG - 正在解析第 7 行数据
2025-07-02 18:37:01 - root - DEBUG - 第 7 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-07-02 18:37:01 - root - DEBUG - 正在解析第 8 行数据
2025-07-02 18:37:01 - root - DEBUG - 第 8 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-07-02 18:37:01 - root - DEBUG - 正在解析第 9 行数据
2025-07-02 18:37:01 - root - DEBUG - 第 9 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-07-02 18:37:01 - root - DEBUG - 正在解析第 10 行数据
2025-07-02 18:37:01 - root - DEBUG - 第 10 行 '已有零件处理建议' 列的数据: 在制在购=None, 库存=None, 使用中=None, 变更成本=None
2025-07-02 18:37:01 - root - DEBUG - 在第 11 行检测到无效数据行，停止解析
2025-07-02 18:37:01 - root - INFO - 从文件 工程变更单_20062_ECN-20023-250126-10.xlsx 中提取了 6 条记录
2025-07-02 18:37:01 - root - INFO - 变更单文档解析成功: 工程变更单_20062_ECN-20023-250126-10.xlsx, 包含 6 个条目
2025-07-02 18:37:01 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-07-02 18:37:01 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-07-02 18:37:01 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-07-02 18:37:01 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-07-02 18:37:01 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-07-02 18:37:01 - root - DEBUG - 计算金额: 2.0 * 0.00 = 0.00
2025-07-02 18:37:01 - root - DEBUG - 从文档 工程变更单_20062_ECN-20023-250126-10.xlsx 中添加了 6 个条目
2025-07-02 18:37:01 - root - INFO - 已将预解析的文档添加到管理器: 工程变更单_20062_ECN-20023-250126-10.xlsx
2025-07-02 18:37:01 - root - DEBUG - 后台任务完成: parse_files_in_background
2025-07-02 18:37:03 - root - DEBUG - 已设置变更单管理表格初始列宽
2025-07-02 18:37:03 - root - DEBUG - 数据变更事件触发
2025-07-02 18:37:05 - root - DEBUG - 切换到标签页: 变更单内容
2025-07-02 18:37:07 - root - DEBUG - DataManager: get_all_items_data returning 6 items.
2025-07-02 18:37:07 - root - DEBUG - 特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...
2025-07-02 18:37:07 - root - DEBUG - 找到的目标零件总数: 0
2025-07-02 18:37:07 - root - DEBUG - 包含'报废'值的目标零件: False
2025-07-02 18:37:07 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=5
2025-07-02 18:37:07 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-07-02 18:37:07 - root - DEBUG - 加载第 1 页数据，行数: 6
2025-07-02 18:37:07 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-07-02 18:37:07 - root - DEBUG - self._checked_indices 属性不存在
2025-07-02 18:37:07 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:37:07 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:37:07 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:37:07 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:37:07 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:07 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:07 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:07 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:07 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:07 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:07 - root - INFO - 计算总价: 6 个项目, 总额 = 0.00
2025-07-02 18:37:07 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 0.00
2025-07-02 18:37:07 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-07-02 18:37:07 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:37:07 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:37:07 - root - INFO - 成功获取变更单内容，共 6 条记录
2025-07-02 18:37:35 - root - DEBUG - 切换到标签页: 变更单管理
2025-07-02 18:37:35 - root - DEBUG - 切换到标签页: 变更单内容
2025-07-02 18:37:36 - root - DEBUG - 切换到标签页: 变更单管理
2025-07-02 18:37:36 - root - DEBUG - 切换到标签页: 变更单内容
2025-07-02 18:37:43 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - DEBUG - 后台任务开始: fetch_prices_in_background
2025-07-02 18:37:43 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:37:43 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:37:43 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:37:43 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:37:43 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:37:43 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - DEBUG - 正在从物料表 XC_OrdinaryMaterials 查询零件 '400160003' 的单价
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160179' 的单价
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160196' 的单价
2025-07-02 18:37:43 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160003' 的单价
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:37:43 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160175' 的单价
2025-07-02 18:37:43 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160173' 的单价
2025-07-02 18:37:43 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160179' 的单价
2025-07-02 18:37:43 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160173' 的单价
2025-07-02 18:37:43 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160175' 的单价
2025-07-02 18:37:43 - root - INFO - 数据库连接已关闭
2025-07-02 18:37:43 - root - INFO - 数据库连接已关闭
2025-07-02 18:37:43 - root - INFO - 数据库连接已关闭
2025-07-02 18:37:43 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160003' 的单价
2025-07-02 18:37:43 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160196' 的单价
2025-07-02 18:37:43 - root - INFO - 数据库连接已关闭
2025-07-02 18:37:43 - root - INFO - 数据库连接已关闭
2025-07-02 18:37:43 - root - DEBUG - 查询到零件 '400160003' 的单价为: 4030.00
2025-07-02 18:37:43 - root - INFO - 数据库连接已关闭
2025-07-02 18:37:43 - root - DEBUG - 计算金额: 2.0 * 4030.00 = 8060.00
2025-07-02 18:37:43 - root - INFO - 单价更新完成，共更新 1 个零件的单价
2025-07-02 18:37:43 - root - DEBUG - 后台任务完成: fetch_prices_in_background
2025-07-02 18:37:43 - root - INFO - 用户触发的单价更新完成，共更新 1 条记录
2025-07-02 18:37:43 - root - DEBUG - 开始筛选，条件: {}
2025-07-02 18:37:43 - root - DEBUG - 没有筛选条件，返回所有条目
2025-07-02 18:37:43 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=5
2025-07-02 18:37:43 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-07-02 18:37:43 - root - DEBUG - 加载第 1 页数据，行数: 6
2025-07-02 18:37:43 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-07-02 18:37:43 - root - DEBUG - self._checked_indices类型: <class 'set'>
2025-07-02 18:37:43 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:37:43 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:37:43 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:37:43 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:37:43 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:43 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:43 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:43 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:43 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:43 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:37:43 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:37:43 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 8060.00
2025-07-02 18:37:43 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-07-02 18:37:43 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:37:43 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:39:59 - root - DEBUG - 选中行 0，数据索引 0
2025-07-02 18:39:59 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - INFO - 计算总价: 1 个项目, 总额 = 8060.00
2025-07-02 18:39:59 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 8060.00
2025-07-02 18:39:59 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 8060.00
2025-07-02 18:39:59 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:39:59 - root - DEBUG - 点击勾选框: 行=0, 状态=CheckState.Checked
2025-07-02 18:39:59 - root - DEBUG - 取消选中行 1，数据索引 1
2025-07-02 18:39:59 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:39:59 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:39:59 - root - INFO - 计算总价: 1 个项目, 总额 = 8060.00
2025-07-02 18:39:59 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 8060.00
2025-07-02 18:39:59 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 8060.00
2025-07-02 18:39:59 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:39:59 - root - DEBUG - 点击勾选框: 行=1, 状态=CheckState.Unchecked
2025-07-02 18:40:00 - root - DEBUG - 选中行 1，数据索引 1
2025-07-02 18:40:00 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:40:00 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:00 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:00 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:00 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:00 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:00 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:00 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:40:00 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:00 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:00 - root - INFO - 计算总价: 2 个项目, 总额 = 8060.00
2025-07-02 18:40:00 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 8060.00
2025-07-02 18:40:00 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 8060.00
2025-07-02 18:40:00 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:40:00 - root - DEBUG - 点击勾选框: 行=1, 状态=CheckState.Checked
2025-07-02 18:40:00 - root - DEBUG - 表头鼠标点击: 索引=0, 位置=(20, 33)
2025-07-02 18:40:01 - root - DEBUG - 表头鼠标点击: 索引=0, 位置=(20, 27)
2025-07-02 18:40:02 - root - DEBUG - 表头鼠标点击: 索引=0, 位置=(24, 25)
2025-07-02 18:40:02 - root - DEBUG - 点击全选框: 新状态=True
2025-07-02 18:40:02 - root - DEBUG - 全选：选中所有 6 条记录
2025-07-02 18:40:02 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:02 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:40:02 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 8060.00
2025-07-02 18:40:02 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 8060.00
2025-07-02 18:40:02 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:40:05 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - DEBUG - 后台任务开始: fetch_prices_in_background
2025-07-02 18:40:05 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:40:05 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:40:05 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:40:05 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:40:05 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:40:05 - root - DEBUG - DatabaseManager instance created.
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160179' 的单价
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160003' 的单价
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - INFO - SQL Server 数据库连接成功
2025-07-02 18:40:05 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160175' 的单价
2025-07-02 18:40:05 - root - DEBUG - 正在从物料表 XC_OrdinaryMaterials 查询零件 '400160003' 的单价
2025-07-02 18:40:05 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160196' 的单价
2025-07-02 18:40:05 - root - DEBUG - 正在从返修表 XC_RepairFee 查询零件 '400160173' 的单价
2025-07-02 18:40:05 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160003' 的单价
2025-07-02 18:40:05 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160175' 的单价
2025-07-02 18:40:05 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160173' 的单价
2025-07-02 18:40:05 - root - INFO - 数据库连接已关闭
2025-07-02 18:40:05 - root - INFO - 数据库连接已关闭
2025-07-02 18:40:05 - root - INFO - 数据库连接已关闭
2025-07-02 18:40:05 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160196' 的单价
2025-07-02 18:40:05 - root - DEBUG - 在表 XC_RepairFee 中未找到零件 '400160179' 的单价
2025-07-02 18:40:05 - root - INFO - 数据库连接已关闭
2025-07-02 18:40:05 - root - INFO - 数据库连接已关闭
2025-07-02 18:40:05 - root - DEBUG - 查询到零件 '400160003' 的单价为: 4030.00
2025-07-02 18:40:05 - root - INFO - 数据库连接已关闭
2025-07-02 18:40:05 - root - DEBUG - 计算金额: 2.0 * 4030.00 = 8060.00
2025-07-02 18:40:05 - root - INFO - 单价更新完成，共更新 1 个零件的单价
2025-07-02 18:40:05 - root - DEBUG - 后台任务完成: fetch_prices_in_background
2025-07-02 18:40:05 - root - INFO - 用户触发的单价更新完成，共更新 1 条记录
2025-07-02 18:40:05 - root - DEBUG - 开始筛选，条件: {}
2025-07-02 18:40:05 - root - DEBUG - 没有筛选条件，返回所有条目
2025-07-02 18:40:05 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=5
2025-07-02 18:40:05 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-07-02 18:40:05 - root - DEBUG - 加载第 1 页数据，行数: 6
2025-07-02 18:40:05 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-07-02 18:40:05 - root - DEBUG - self._checked_indices类型: <class 'set'>
2025-07-02 18:40:05 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:40:05 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:40:05 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:40:05 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:40:05 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:40:05 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 8060.00
2025-07-02 18:40:05 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 8060.00
2025-07-02 18:40:05 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:40:05 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:41:01 - root - DEBUG - ECNContentPage: '取消过滤'按钮被点击
2025-07-02 18:41:01 - root - DEBUG - 开始筛选，条件: {}
2025-07-02 18:41:01 - root - DEBUG - 没有筛选条件，返回所有条目
2025-07-02 18:41:01 - root - DEBUG - ECNContentPage: DataManager 应用了空筛选，_filtered_data 更新为 6 条记录
2025-07-02 18:41:01 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=5
2025-07-02 18:41:01 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-07-02 18:41:01 - root - DEBUG - 加载第 1 页数据，行数: 6
2025-07-02 18:41:01 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-07-02 18:41:01 - root - DEBUG - self._checked_indices类型: <class 'set'>
2025-07-02 18:41:01 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:41:01 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:41:01 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:41:01 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:41:01 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:01 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:01 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:01 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:01 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:01 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:01 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:41:01 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 8060.00
2025-07-02 18:41:01 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-07-02 18:41:01 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:41:01 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:41:01 - root - INFO - 已取消所有筛选条件
2025-07-02 18:41:02 - root - DEBUG - ECNContentPage: '取消过滤'按钮被点击
2025-07-02 18:41:02 - root - DEBUG - 开始筛选，条件: {}
2025-07-02 18:41:02 - root - DEBUG - 没有筛选条件，返回所有条目
2025-07-02 18:41:02 - root - DEBUG - ECNContentPage: DataManager 应用了空筛选，_filtered_data 更新为 6 条记录
2025-07-02 18:41:02 - root - DEBUG - 当前页要加载的数据: 页码=1, 起始索引=0, 结束索引=5
2025-07-02 18:41:02 - root - DEBUG - 在加载表格数据前检查目标零件:
2025-07-02 18:41:02 - root - DEBUG - 加载第 1 页数据，行数: 6
2025-07-02 18:41:02 - root - DEBUG - self._checked_rows类型: <class 'set'>
2025-07-02 18:41:02 - root - DEBUG - self._checked_indices类型: <class 'set'>
2025-07-02 18:41:02 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:41:02 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:41:02 - root - DEBUG - 已设置表格初始列宽
2025-07-02 18:41:02 - root - DEBUG - ECNContentPage._update_price_display: CALLED. len(self._filtered_data)=6
2025-07-02 18:41:02 - root - DEBUG - 处理项目金额: key='金额', value_str='8060.00', item_data={'零件名称': '切平座', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '换图装配', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '4030.00', '金额': '8060.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160003', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160173', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160175', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160179', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:02 - root - DEBUG - 处理项目金额: key='金额', value_str='0.00', item_data={'零件名称': '切平盘', '零件编码': '400160196', 'ERP号': '20062', '计划号': 'SCJH20230251', '所属设备名称': 'Busbar切平', '所属项目名称': '广汽锐湃IDU定子装配线\n（第一条线）', '所属项目编号': 'CN23HA005', '所属变更单单号': 'ECN-20023-250126-10', '新版本实施内容': '返修', '变更原因': '设计优化', '问题分类': '105', '工号': '20023', '变更日期': '250126', '数量': '2.0', '单价': '0.00', '金额': '0.00', '已有零件处理意见-在制在购': '', '已有零件处理意见-库存': '', '已有零件处理意见-使用中': '', '已有零件处理意见-变更成本': ''}
2025-07-02 18:41:02 - root - INFO - 计算总价: 6 个项目, 总额 = 8060.00
2025-07-02 18:41:02 - root - DEBUG - ECNContentPage._update_price_display: Calculated total_price = 8060.00
2025-07-02 18:41:02 - root - DEBUG - ECNContentPage._update_price_display: Calculated selected_price = 0.00
2025-07-02 18:41:02 - root - DEBUG - ECNContentPage._update_price_display: FINISHED.
2025-07-02 18:41:02 - root - INFO - 刷新表格完成，总行数: 6
2025-07-02 18:41:02 - root - INFO - 已取消所有筛选条件
2025-07-02 18:41:06 - root - DEBUG - 切换到标签页: 变更单管理
2025-07-02 18:41:07 - root - DEBUG - 切换到标签页: 变更单内容
2025-07-02 18:41:08 - root - DEBUG - 切换到标签页: 变更单管理
2025-07-02 18:41:09 - root - DEBUG - 切换到标签页: 变更单内容
2025-07-02 18:41:56 - root - INFO - 应用程序正常退出
