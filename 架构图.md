ECN_CostManage/
├── main.py                    # 程序入口点
│
├── config.py                  # 全局配置文件
│
├── views/                     # 界面层
│   ├── main_window.py         # 主窗口
│   ├── ecn_management.py      # 变更单管理页面
│   ├── ecn_content.py         # 变更单内容页面
│   └── custom_widgets.py      # 自定义控件
│
├── models/                    # 数据模型层
│   ├── ecn_document.py        # 变更单文档类
│   ├── ecn_item.py            # 变更单条目类
│   └── data_manager.py        # 数据管理类
│
├── utils/                     # 工具层
│   ├── excel_parser.py        # Excel解析工具
│   ├── file_manager.py        # 文件管理工具
│   ├── logger.py              # 日志工具
│   └── calculation.py         # 计算工具
│
├── resources/                 # 资源层
│   ├── Styles.qss            # 界面样式文件
│   └── icons/                # 图标资源
│
└── tests/                     # 测试层
    ├── test_excel_parser.py   # Excel解析测试
    └── test_data_manager.py   # 数据管理测试


                 ┌─────────────┐
                  │   main.py   │
                  └──────┬──────┘
                         │
         ┌───────────────┼───────────────┐
         │               │               │
┌────────▼─────────┐     │         ┌─────▼──────┐
│  views/          │     │         │  models/   │
│  - main_window   │◄────┼────────►│            │
│  - ecn_management│     │         │            │
│  - ecn_content   │     │         │            │
└─────────┬────────┘     │         └──────┬─────┘
          │              │                │
          │        ┌─────▼─────┐          │
          └───────►│  utils/   │◄─────────┘
                   │           │
                   └───────────┘



┌─────────────┐    解析    ┌─────────────┐    展示    ┌─────────────┐
│  Excel文件  │───────────►│ 数据模型层  │───────────►│  用户界面   │
└─────────────┘           └──────┬──────┘           └──────┬──────┘
                                 │                         │
                          ┌──────▼──────┐          ┌───────▼───────┐
                          │  计算处理   │◄─────────┤ 用户交互操作  │
                          └─────────────┘          └───────────────┘         


