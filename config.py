#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全局配置文件，存储应用程序的全局配置参数和常量
"""

# 应用程序基本信息
APP_NAME = "ECN成本管理系统"
APP_VERSION = "1.0.0"

# 日志配置
LOG_LEVEL = "DEBUG"  # 可选: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE = "ecn_cost.log"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# 表格配置
MAX_ROWS_PER_PAGE = 100  # 每页最多显示的行数
DECIMAL_PLACES = 2       # 金额保留小数位数

# 价格信息显示配置
SHOW_PRICE_INFO = True   # 是否显示价格相关信息（True: 显示, False: 隐藏）

# Excel解析配置
EXCEL_FIXED_CELLS = {
    "ERP号": "B2",
    "设备名称": "D2",
    "项目编号": "H2",
    "项目名称": "K2",
    "变更单号": "N2"
}

EXCEL_HEADERS = {
    "序号": "A3",
    "变更原因": "B3",
    "零件编码": "C3",
    "新版本": "D3",
    "名称": "E3",
    "材料": "F3",
    "新版本实施内容": "G3",
    "单台数量": "H3",
    "台数": "I3",
    "总数量": "J3",
    "涉及计划号": "K3",
    "需求日期": "L3",
    "问题分类": "M3",
    "旧版本": "N3",
    "在制在购": "O4",
    "库存": "P4",
    "使用中": "Q4",
    #"变更成本": "R4"
}

# 数据表开始行和结束标识
DATA_START_ROW = 5
DATA_END_MARKER = "填写说明"

# 导出格式设置
EXPORT_FORMATS = ["xlsx", "csv"]

# 资源文件路径
STYLE_FILE = "resources/Styles.qss"
ICON_FOLDER = "resources/icons/"

# 数据库配置 (SQL Server)
DB_CONFIG = {
    "driver": "{ODBC Driver 17 for SQL Server}",  # 请确保已安装此驱动，或替换为其他可用驱动
    "server": "*************",                   # 数据库服务器IP或名称
    "port": "1433",                              # SQL Server 默认端口
    "database": "U9C",                           # 数据库名称
    "user": "uptecadmin1",                       # 用户名
    "password": "Uptec@123"                      # 密码
}

# 数据库表配置
DB_TABLES = {
    "ordinary_materials": "XC_OrdinaryMaterials",
    "repair_fee": "XC_RepairFee"
}

settings = {
    'WINDOW_WIDTH': 2150,
    'WINDOW_HEIGHT': 1000,
}