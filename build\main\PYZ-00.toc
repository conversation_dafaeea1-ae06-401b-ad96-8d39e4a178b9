('D:\\Uptec '
 'Working\\develop\\ECN_Cost\\ECN_CostManage\\build\\main\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt6',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\calendar.py',
   'PYMODULE'),
  ('cmd', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\cmd.py', 'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config',
   'D:\\Uptec Working\\develop\\ECN_Cost\\ECN_CostManage\\config.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\copy.py',
   'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\dis.py', 'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('models',
   'D:\\Uptec Working\\develop\\ECN_Cost\\ECN_CostManage\\models\\__init__.py',
   'PYMODULE'),
  ('models.data_manager',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\models\\data_manager.py',
   'PYMODULE'),
  ('models.ecn_document',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\models\\ecn_document.py',
   'PYMODULE'),
  ('models.ecn_item',
   'D:\\Uptec Working\\develop\\ECN_Cost\\ECN_CostManage\\models\\ecn_item.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__main__',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\__main__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py._src_pyf',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\_src_pyf.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\random.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\runpy.py',
   'PYMODULE'),
  ('scipy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\__init__.py',
   'PYMODULE'),
  ('scipy.__config__',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy._lib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._array_api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_array_api.py',
   'PYMODULE'),
  ('scipy._lib._array_api_no_0d',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_array_api_no_0d.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy._lib._elementwise_iterative_method',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_elementwise_iterative_method.py',
   'PYMODULE'),
  ('scipy._lib._finite_differences',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_finite_differences.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat._internal',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\_internal.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._aliases',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._fft',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._helpers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._linalg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._aliases',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._info',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._aliases',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._info',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._aliases',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._info',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.fft',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.linalg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._aliases',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._info',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._funcs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_funcs.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.framework',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\framework.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.main',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\main.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.models',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\models.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.problem',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\problem.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.settings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\settings.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.geometry',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.optim',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\optim.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\exceptions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.math',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\math.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.versions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\versions.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy.conftest',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\conftest.py',
   'PYMODULE'),
  ('scipy.constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.fft',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.fft._basic_backend',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_basic_backend.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_backend',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_fftlog_backend.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms_backend',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\fft\\_realtransforms_backend.py',
   'PYMODULE'),
  ('scipy.integrate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.integrate._cubature',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_cubature.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._lebedev',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_lebedev.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.integrate._rules',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_rules\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._rules._base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_rules\\_base.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_kronrod',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_kronrod.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_legendre',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_legendre.py',
   'PYMODULE'),
  ('scipy.integrate._rules._genz_malik',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_rules\\_genz_malik.py',
   'PYMODULE'),
  ('scipy.integrate._tanhsinh',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\_tanhsinh.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate._bary_rational',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_bary_rational.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_repro',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_repro.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._ndbspline',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_ndbspline.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.interpnd',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\interpnd.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.linalg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage._delegators',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_delegators.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._ndimage_api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_ndimage_api.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy.ndimage._support_alternative_backends',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.optimize',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._bracket',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_bracket.py',
   'PYMODULE'),
  ('scipy.optimize._chandrupatla',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_chandrupatla.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy.optimize._cobyqa_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_cobyqa_py.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._dcsrch',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_dcsrch.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._highspy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_highspy\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._highspy._highs_wrapper',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_wrapper.py',
   'PYMODULE'),
  ('scipy.optimize._isotonic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_isotonic.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._complex',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_complex.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._vertex',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_vertex.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.sparse',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._matrix',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_matrix.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.linalg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._special_sparse_arrays',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_special_sparse_arrays.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.spatial',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.special',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special._add_newdocs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.special._input_validation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_input_validation.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._mptestutils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_mptestutils.py',
   'PYMODULE'),
  ('scipy.special._multiufuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_multiufuncs.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._precompute',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\__init__.py',
   'PYMODULE'),
  ('scipy.special._precompute.cosine_cdf',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\cosine_cdf.py',
   'PYMODULE'),
  ('scipy.special._precompute.expn_asy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\expn_asy.py',
   'PYMODULE'),
  ('scipy.special._precompute.gammainc_asy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\gammainc_asy.py',
   'PYMODULE'),
  ('scipy.special._precompute.gammainc_data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\gammainc_data.py',
   'PYMODULE'),
  ('scipy.special._precompute.hyp2f1_data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\hyp2f1_data.py',
   'PYMODULE'),
  ('scipy.special._precompute.lambertw',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\lambertw.py',
   'PYMODULE'),
  ('scipy.special._precompute.loggamma',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\loggamma.py',
   'PYMODULE'),
  ('scipy.special._precompute.struve_convergence',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\struve_convergence.py',
   'PYMODULE'),
  ('scipy.special._precompute.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\utils.py',
   'PYMODULE'),
  ('scipy.special._precompute.wright_bessel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\wright_bessel.py',
   'PYMODULE'),
  ('scipy.special._precompute.wright_bessel_data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\wright_bessel_data.py',
   'PYMODULE'),
  ('scipy.special._precompute.wrightomega',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\wrightomega.py',
   'PYMODULE'),
  ('scipy.special._precompute.zetac',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_precompute\\zetac.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special._support_alternative_backends',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.special._testutils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\_testutils.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.special.tests',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\__init__.py',
   'PYMODULE'),
  ('scipy.special.tests.data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\data\\__init__.py',
   'PYMODULE'),
  ('scipy.special.tests.test_basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_basic.py',
   'PYMODULE'),
  ('scipy.special.tests.test_bdtr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_bdtr.py',
   'PYMODULE'),
  ('scipy.special.tests.test_boost_ufuncs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_boost_ufuncs.py',
   'PYMODULE'),
  ('scipy.special.tests.test_boxcox',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_boxcox.py',
   'PYMODULE'),
  ('scipy.special.tests.test_cdflib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_cdflib.py',
   'PYMODULE'),
  ('scipy.special.tests.test_cdft_asymptotic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_cdft_asymptotic.py',
   'PYMODULE'),
  ('scipy.special.tests.test_cephes_intp_cast',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_cephes_intp_cast.py',
   'PYMODULE'),
  ('scipy.special.tests.test_cosine_distr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_cosine_distr.py',
   'PYMODULE'),
  ('scipy.special.tests.test_cython_special',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_cython_special.py',
   'PYMODULE'),
  ('scipy.special.tests.test_data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_data.py',
   'PYMODULE'),
  ('scipy.special.tests.test_dd',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_dd.py',
   'PYMODULE'),
  ('scipy.special.tests.test_digamma',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_digamma.py',
   'PYMODULE'),
  ('scipy.special.tests.test_ellip_harm',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_ellip_harm.py',
   'PYMODULE'),
  ('scipy.special.tests.test_erfinv',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_erfinv.py',
   'PYMODULE'),
  ('scipy.special.tests.test_exponential_integrals',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_exponential_integrals.py',
   'PYMODULE'),
  ('scipy.special.tests.test_extending',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_extending.py',
   'PYMODULE'),
  ('scipy.special.tests.test_faddeeva',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_faddeeva.py',
   'PYMODULE'),
  ('scipy.special.tests.test_gamma',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_gamma.py',
   'PYMODULE'),
  ('scipy.special.tests.test_gammainc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_gammainc.py',
   'PYMODULE'),
  ('scipy.special.tests.test_hyp2f1',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_hyp2f1.py',
   'PYMODULE'),
  ('scipy.special.tests.test_hypergeometric',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_hypergeometric.py',
   'PYMODULE'),
  ('scipy.special.tests.test_iv_ratio',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_iv_ratio.py',
   'PYMODULE'),
  ('scipy.special.tests.test_kolmogorov',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_kolmogorov.py',
   'PYMODULE'),
  ('scipy.special.tests.test_lambertw',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_lambertw.py',
   'PYMODULE'),
  ('scipy.special.tests.test_legendre',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_legendre.py',
   'PYMODULE'),
  ('scipy.special.tests.test_log_softmax',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_log_softmax.py',
   'PYMODULE'),
  ('scipy.special.tests.test_loggamma',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_loggamma.py',
   'PYMODULE'),
  ('scipy.special.tests.test_logit',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_logit.py',
   'PYMODULE'),
  ('scipy.special.tests.test_logsumexp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_logsumexp.py',
   'PYMODULE'),
  ('scipy.special.tests.test_mpmath',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_mpmath.py',
   'PYMODULE'),
  ('scipy.special.tests.test_nan_inputs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_nan_inputs.py',
   'PYMODULE'),
  ('scipy.special.tests.test_ndtr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_ndtr.py',
   'PYMODULE'),
  ('scipy.special.tests.test_ndtri_exp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_ndtri_exp.py',
   'PYMODULE'),
  ('scipy.special.tests.test_orthogonal',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_orthogonal.py',
   'PYMODULE'),
  ('scipy.special.tests.test_orthogonal_eval',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_orthogonal_eval.py',
   'PYMODULE'),
  ('scipy.special.tests.test_owens_t',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_owens_t.py',
   'PYMODULE'),
  ('scipy.special.tests.test_pcf',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_pcf.py',
   'PYMODULE'),
  ('scipy.special.tests.test_pdtr',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_pdtr.py',
   'PYMODULE'),
  ('scipy.special.tests.test_powm1',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_powm1.py',
   'PYMODULE'),
  ('scipy.special.tests.test_precompute_expn_asy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_precompute_expn_asy.py',
   'PYMODULE'),
  ('scipy.special.tests.test_precompute_gammainc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_precompute_gammainc.py',
   'PYMODULE'),
  ('scipy.special.tests.test_precompute_utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_precompute_utils.py',
   'PYMODULE'),
  ('scipy.special.tests.test_round',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_round.py',
   'PYMODULE'),
  ('scipy.special.tests.test_sf_error',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_sf_error.py',
   'PYMODULE'),
  ('scipy.special.tests.test_sici',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_sici.py',
   'PYMODULE'),
  ('scipy.special.tests.test_specfun',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_specfun.py',
   'PYMODULE'),
  ('scipy.special.tests.test_spence',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_spence.py',
   'PYMODULE'),
  ('scipy.special.tests.test_spfun_stats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special.tests.test_sph_harm',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_sph_harm.py',
   'PYMODULE'),
  ('scipy.special.tests.test_spherical_bessel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special.tests.test_support_alternative_backends',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.special.tests.test_trig',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_trig.py',
   'PYMODULE'),
  ('scipy.special.tests.test_ufunc_signatures',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_ufunc_signatures.py',
   'PYMODULE'),
  ('scipy.special.tests.test_wright_bessel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_wright_bessel.py',
   'PYMODULE'),
  ('scipy.special.tests.test_wrightomega',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_wrightomega.py',
   'PYMODULE'),
  ('scipy.special.tests.test_xsf_cuda',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_xsf_cuda.py',
   'PYMODULE'),
  ('scipy.special.tests.test_zeta',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\special\\tests\\test_zeta.py',
   'PYMODULE'),
  ('scipy.stats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._bws_test',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_bws_test.py',
   'PYMODULE'),
  ('scipy.stats._censored_data',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_censored_data.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._correlation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_correlation.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._distribution_infrastructure',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_distribution_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._mgc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_mgc.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._multicomp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_multicomp.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats._new_distributions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_new_distributions.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._probability_distribution',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_probability_distribution.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.stats._qmvnt',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_qmvnt.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats._sensitivity_analysis',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_sensitivity_analysis.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._survival',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_survival.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.stats._wilcoxon',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\_wilcoxon.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\tty.py', 'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\typing.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('utils',
   'D:\\Uptec Working\\develop\\ECN_Cost\\ECN_CostManage\\utils\\__init__.py',
   'PYMODULE'),
  ('utils.calculation',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\utils\\calculation.py',
   'PYMODULE'),
  ('utils.database_manager',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\utils\\database_manager.py',
   'PYMODULE'),
  ('utils.excel_parser',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\utils\\excel_parser.py',
   'PYMODULE'),
  ('utils.file_manager',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\utils\\file_manager.py',
   'PYMODULE'),
  ('utils.logger',
   'D:\\Uptec Working\\develop\\ECN_Cost\\ECN_CostManage\\utils\\logger.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\uuid.py',
   'PYMODULE'),
  ('views',
   'D:\\Uptec Working\\develop\\ECN_Cost\\ECN_CostManage\\views\\__init__.py',
   'PYMODULE'),
  ('views.background_tasks',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\views\\background_tasks.py',
   'PYMODULE'),
  ('views.custom_widgets',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\views\\custom_widgets.py',
   'PYMODULE'),
  ('views.ecn_content',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\views\\ecn_content.py',
   'PYMODULE'),
  ('views.ecn_management',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\views\\ecn_management.py',
   'PYMODULE'),
  ('views.main_window',
   'D:\\Uptec '
   'Working\\develop\\ECN_Cost\\ECN_CostManage\\views\\main_window.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xlrd',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\__init__.py',
   'PYMODULE'),
  ('xlrd.biffh',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\biffh.py',
   'PYMODULE'),
  ('xlrd.book',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\book.py',
   'PYMODULE'),
  ('xlrd.compdoc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\compdoc.py',
   'PYMODULE'),
  ('xlrd.formatting',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\formatting.py',
   'PYMODULE'),
  ('xlrd.formula',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\formula.py',
   'PYMODULE'),
  ('xlrd.info',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\info.py',
   'PYMODULE'),
  ('xlrd.sheet',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\sheet.py',
   'PYMODULE'),
  ('xlrd.timemachine',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\timemachine.py',
   'PYMODULE'),
  ('xlrd.xldate',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\site-packages\\xlrd\\xldate.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\.conda\\envs\\ECN_COST\\Lib\\zipimport.py',
   'PYMODULE')])
