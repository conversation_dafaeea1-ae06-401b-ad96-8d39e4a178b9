#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
变更单内容管理页面模块，实现内容展示、筛选和计算功能
"""

from PyQt6.QtCore import Qt, pyqtSignal, QRect, QTimer, QThreadPool
from PyQt6.QtWidgets import (
    QWidget, QTableWidget, QTableWidgetItem, QPushButton, QLabel,
    QVBoxLayout, QHBoxLayout, QMessageBox, QHeaderView, QItemDelegate,
    QLineEdit, QStyledItemDelegate, QCheckBox, QStyle, QStyleOptionHeader,
    QProgressDialog, QComboBox, QFileDialog
)
from PyQt6.QtGui import QDoubleValidator, QPen, QColor, QStandardItemModel, QStandardItem
import config
from utils.logger import info, debug, error
from models.data_manager import data_manager
from utils.calculation import calculator
from utils.database_manager import DatabaseManager
from views.custom_widgets import FilterHeader, PaginationWidget, TotalPriceWidget
from .background_tasks import Worker
from typing import List, Any, Dict
import pandas as pd
import os

class UnitPriceDelegate(QStyledItemDelegate):
    """单价编辑代理，用于编辑单价列"""
    
    def __init__(self, parent=None):
        """
        初始化单价编辑代理
        
        Args:
            parent: 父部件
        """
        super().__init__(parent)
    
    def createEditor(self, parent, option, index):
        """创建编辑器"""
        editor = QLineEdit(parent)
        # 设置验证器，只允许输入数字和小数点
        validator = QDoubleValidator(0, 1000000, config.DECIMAL_PLACES, editor)
        validator.setNotation(QDoubleValidator.Notation.StandardNotation)
        editor.setValidator(validator)
        return editor
    
    def setEditorData(self, editor, index):
        """设置编辑器数据"""
        value = index.model().data(index, Qt.ItemDataRole.DisplayRole)
        if value is not None:
            editor.setText(str(value))
    
    def setModelData(self, editor, model, index):
        """将编辑器的数据设置到模型中"""
        try:
            # 获取编辑器的文本
            text = editor.text().strip()
            
            # 如果文本为空，则设置为0
            if not text:
                text = "0"
            
            # 尝试转换为浮点数以验证
            try:
                value = float(text)
                formatted = f"{value:.{config.DECIMAL_PLACES}f}"
            except ValueError:
                QMessageBox.warning(None, "无效输入", "请输入有效的数字。")
                return
            
            # 更新UI模型
            model.setData(index, formatted, Qt.ItemDataRole.DisplayRole)
            
            # 如果更新成功，更新底层数据
            row = index.row()
            debug(f"UnitPriceDelegate: 更新单价: row={row}, value={formatted}")
            
            # 获取所有页面的基准索引
            page_size = index.model().parent().pagination.get_rows_per_page()
            current_page = index.model().parent().pagination.get_current_page()
            data_index = (current_page - 1) * page_size + row
            
            # 更新DataManager中的数据
            if data_manager.update_item_unit_price(data_index, formatted):
                debug(f"成功更新条目单价: 索引={data_index}, 单价={formatted}")
                
                # 获取当前显示的行数据并更新金额
                row_data = index.model().parent()._filtered_data[data_index].copy()
                quantity = float(row_data.get("数量", "0"))
                unit_price = float(formatted)
                amount = quantity * unit_price
                amount_formatted = f"{amount:.{config.DECIMAL_PLACES}f}"
                
                # 查找金额列的索引
                amount_col = -1
                for col, name in enumerate(index.model().parent()._column_names):
                    if name == "金额":
                        amount_col = col
                        break
                
                # 更新金额单元格
                if amount_col >= 0:
                    amount_index = model.index(row, amount_col)
                    model.setData(amount_index, amount_formatted, Qt.ItemDataRole.DisplayRole)
                    debug(f"更新金额单元格: row={row}, col={amount_col}, value={amount_formatted}")
                
                # 更新总价显示
                index.model().parent()._update_price_display()
            else:
                error(f"更新条目单价失败: 索引={data_index}")
                
        except Exception as e:
            error(f"设置单价时出错: {str(e)}")
            QMessageBox.warning(None, "操作失败", f"更新单价时出错: {str(e)}")


class CheckBoxFilterHeader(FilterHeader):
    """
    带有勾选框的过滤表头，同时支持勾选功能和过滤功能
    """
    checkBoxClicked = pyqtSignal(bool)
    
    def __init__(self, orientation, parent=None):
        """初始化带勾选框的过滤表头"""
        super().__init__(orientation, parent)
        self.isChecked = False
        self.checkBoxRect = None
        # 禁用表头的上下文菜单
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        # 连接自定义上下文菜单信号，但不做任何处理
        self.customContextMenuRequested.connect(self._doNothing)
        
        # 默认启用所有列的点击功能，但在paintSection中特别处理第一列
        self.setSectionsClickable(True)
        
        # 在初始化时禁用第一列的筛选
        self.disable_filter_for_column(0)
        
        # 添加调试日志
        debug("初始化带勾选框的筛选表头")
        
        # 确保在初始化后立即刷新表头的显示
        QTimer.singleShot(100, lambda: self.viewport().update())
        debug("安排了表头的延迟更新")
    
    def _doNothing(self, pos):
        """空函数，用于禁用上下文菜单"""
        pass
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        pos = event.pos()
        logicalIndex = self.logicalIndexAt(pos)
        
        # 调试日志：记录点击位置和区域
        debug(f"表头鼠标点击: 索引={logicalIndex}, 位置=({pos.x()}, {pos.y()})")
        
        # 如果是第一列并且点击了勾选框区域，处理勾选框点击
        if logicalIndex == 0 and self.checkBoxRect and self.checkBoxRect.contains(pos):
            self.isChecked = not self.isChecked
            debug(f"点击全选框: 新状态={self.isChecked}")
            self.checkBoxClicked.emit(self.isChecked)
            self.update()
            event.accept()  # 标记事件已处理
            return
        
        # 对于其他列或区域，调用基类的鼠标点击事件处理过滤器点击
        super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        pos = event.pos()
        logicalIndex = self.logicalIndexAt(pos)
        
        # 如果是第一列，直接处理完成，不触发任何菜单
        if logicalIndex == 0:
            event.accept()
            return
            
        # 对于其他列，调用基类处理
        super().mouseReleaseEvent(event)
    
    def paintSection(self, painter, rect, logicalIndex):
        """绘制表头区域"""
        if logicalIndex == 0:
            # 先调用基类方法绘制标准的表头背景和边框
            opt = QStyleOptionHeader()
            self.initStyleOption(opt)
            opt.rect = rect
            opt.section = logicalIndex
            opt.text = "全选"  # 显示全选文字
            opt.textAlignment = Qt.AlignmentFlag.AlignCenter
            
            # 使用样式绘制标题背景和边框
            painter.save()
            self.style().drawControl(QStyle.ControlElement.CE_Header, opt, painter, self)
            
            # 计算勾选框的位置和大小 - 在中央偏上的位置
            checkbox_size = 18
            self.checkBoxRect = QRect(
                rect.x() + (rect.width() - checkbox_size) // 2,
                rect.y() + rect.height() // 2 - checkbox_size + 2,  # 向上偏移一点
                checkbox_size,
                checkbox_size
            )
            
            # 在勾选框周围绘制明显的边框和背景
            painter.fillRect(self.checkBoxRect.adjusted(-1, -1, 1, 1), QColor(230, 244, 255))  # 扩大背景区域
            
            # 使用更粗的边框和明显的颜色
            pen = QPen(QColor("#0057d8"))  # 更深的蓝色
            pen.setWidth(2)
            painter.setPen(pen)
            painter.drawRect(self.checkBoxRect)
            
            # 如果选中，绘制醒目的勾选标记
            if self.isChecked:
                inner_margin = 4
                painter.fillRect(
                    self.checkBoxRect.x() + inner_margin,
                    self.checkBoxRect.y() + inner_margin,
                    self.checkBoxRect.width() - 2 * inner_margin,
                    self.checkBoxRect.height() - 2 * inner_margin,
                    QColor("#0057d8")
                )
            
            # 绘制"全选"文字
            text_rect = QRect(rect.x(), self.checkBoxRect.bottom() + 2, 
                             rect.width(), rect.height() - self.checkBoxRect.bottom() - 2)
            painter.setPen(QPen(QColor("#333333")))  # 普通文字颜色
            painter.drawText(text_rect, Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop, "全选")
            
            # debug(f"绘制表头全选框: 位置=({self.checkBoxRect.x()}, {self.checkBoxRect.y()}), 大小={checkbox_size}")
            painter.restore()
        else:
            # 对于其他列，正常调用基类的绘制方法
            super().paintSection(painter, rect, logicalIndex)
    
    def sectionSizeFromContents(self, logicalIndex):
        """调整列宽度"""
        size = super().sectionSizeFromContents(logicalIndex)
        if logicalIndex == 0:
            # 为勾选框列设置固定宽度
            size.setWidth(45)
        return size


class ECNContentPage(QWidget):
    """ECN变更单内容管理页面，展示解析后的数据和筛选功能"""
    
    def __init__(self, parent=None):
        """
        初始化ECN变更单内容页面

        Args:
            parent: 父部件
        """
        super().__init__(parent)
        # 添加勾选框列名，第一列使用空字符串
        base_columns = [
            "全选", "零件名称", "零件编码", "ERP号", "计划号", "所属设备名称",
            "所属项目名称", "所属项目编号", "所属变更单单号",
            "新版本实施内容", "变更原因", "问题分类", "工号", "变更日期",
            "数量", "已有零件处理建议\n-在制在购", "已有零件处理建议\n-库存",
            "已有零件处理建议\n-使用中" #"已有零件处理建议\n-变更成本",
        ]

        # 根据配置决定是否添加价格相关列
        if config.SHOW_PRICE_INFO:
            base_columns.extend(["单价", "金额"])

        self._column_names = base_columns
        self._current_data = []
        self._filtered_data = []
        self._selected_rows = set()
        self._checked_rows = set()  # 存储勾选的行索引
        
        self._init_ui()
        self._setup_connections()
    
    def _init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题标签
        title_label = QLabel("ECN变更单内容")
        title_label.setObjectName("titleLabel")
        main_layout.addWidget(title_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 获取内容按钮
        self.fetch_button = QPushButton("变更内容获取")
        self.fetch_button.clicked.connect(self._on_fetch_content)
        button_layout.addWidget(self.fetch_button)
        
        # 获取单价按钮 (根据配置决定是否显示)
        if config.SHOW_PRICE_INFO:
            self.fetch_prices_button = QPushButton("获取零件单价")
            self.fetch_prices_button.clicked.connect(self._on_fetch_prices_clicked)
            button_layout.addWidget(self.fetch_prices_button)
        else:
            self.fetch_prices_button = None
        
        # 取消过滤按钮 (新添加)
        self.clear_filters_button = QPushButton("取消过滤")
        self.clear_filters_button.clicked.connect(self._on_clear_filters_clicked)
        button_layout.addWidget(self.clear_filters_button)
        
        # 导出Excel按钮
        self.export_excel_button = QPushButton("导出Excel")
        self.export_excel_button.clicked.connect(self._on_export_excel_clicked)
        self.export_excel_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        button_layout.addWidget(self.export_excel_button)
        
        button_layout.addStretch(1)

        # 总价显示区域 (根据配置决定是否显示)
        if config.SHOW_PRICE_INFO:
            self.total_price_widget = TotalPriceWidget("总价")
            button_layout.addWidget(self.total_price_widget)

            # 选中价显示区域
            self.selected_price_widget = TotalPriceWidget("选中总价")
            button_layout.addWidget(self.selected_price_widget)
        else:
            self.total_price_widget = None
            self.selected_price_widget = None
        
        main_layout.addLayout(button_layout)
        
        # 表格控件
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(len(self._column_names))
        self.table_widget.setHorizontalHeaderLabels(self._column_names)
        self.table_widget.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table_widget.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setShowGrid(True)
        
        # 设置表格样式，包括表头的显示样式
        self.table_widget.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f5f5f5;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
            QHeaderView::section:first {
                background-color: #e6f4ff;  /* 第一列表头颜色更明显 */
            }
        """)
        
        # 修改为启用单元格内容编辑功能
        self.table_widget.setEditTriggers(QTableWidget.EditTrigger.DoubleClicked | 
                                         QTableWidget.EditTrigger.SelectedClicked)
        
        # 设置自定义表头，同时支持筛选和勾选功能
        self.header = CheckBoxFilterHeader(Qt.Orientation.Horizontal, self.table_widget)
        self.table_widget.setHorizontalHeader(self.header)
        self.header.filterChanged.connect(self._on_filter_changed)
        self.header.checkBoxClicked.connect(self._on_header_checkbox_clicked)
        
        # 禁用第一列的筛选功能
        self.header.disable_filter_for_column(0)
        
        # 设置第一列（勾选框列）的宽度为固定值
        self.header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        self.table_widget.setColumnWidth(0, 45)
        
        # 禁用第一列的排序
        self.table_widget.horizontalHeader().setSortIndicatorShown(True)
        
        # 设置列标题行的高度增加，以便更好地显示勾选框
        self.header.setMinimumHeight(30)
        self.header.setDefaultSectionSize(30)
        
        # 确保表头显示更新
        self.header.viewport().update()
        QTimer.singleShot(500, lambda: self.header.viewport().update())
        
        # 设置单价列可编辑 (仅在显示价格信息时)
        if config.SHOW_PRICE_INFO and "单价" in self._column_names:
            price_column = self._column_names.index("单价")
            self.table_widget.setItemDelegateForColumn(price_column, UnitPriceDelegate())
        
        # 监听单元格变更事件
        self.table_widget.cellChanged.connect(self._on_cell_changed)
        
        # 表格选中行变化
        self.table_widget.itemSelectionChanged.connect(self._on_selection_changed)
        
        # 单元格点击事件
        self.table_widget.itemClicked.connect(self._on_item_clicked)
        
        main_layout.addWidget(self.table_widget)
        
        # 分页控件
        self.pagination = PaginationWidget()
        self.pagination.pageChanged.connect(self._on_page_changed)
        main_layout.addWidget(self.pagination)
        
        # 状态标签
        self.status_label = QLabel("当前没有变更单内容")
        main_layout.addWidget(self.status_label)
        
        self._set_initial_column_widths()
        
        self.setLayout(main_layout)

        # 初始化线程池
        self.thread_pool = QThreadPool()
        info(f"ECN内容页面初始化线程池，最大线程数: {self.thread_pool.maxThreadCount()}")
    
    def _set_initial_column_widths(self):
        """设置表格的初始列宽"""
        try:
            # 设置第一列（勾选框列）的宽度
            self.table_widget.setColumnWidth(0, 45)
            
            # 设置其他列的宽度
            column_widths = {
                "零件名称": 150,
                "零件编码": 150,
                "ERP号": 120,
                "计划号": 120,
                "所属设备名称": 150,
                "所属项目名称": 150,
                "所属项目编号": 120,
                "所属变更单单号": 150,
                "新版本实施内容": 120,
                "变更原因": 150,
                "问题分类": 100,
                "工号": 100,
                "变更日期": 120,
                "数量": 80,
                "已有零件处理建议\n-在制在购": 120,
                "已有零件处理建议\n-库存": 120,
                "已有零件处理建议\n-使用中": 120,
                #"已有零件处理建议\n-变更成本": 120,
                "单价": 100,
                "金额": 100
            }
            
            # 应用设置的列宽
            for col, name in enumerate(self._column_names[1:], 1):  # 从第二列开始
                if name in column_widths:
                    self.table_widget.setColumnWidth(col, column_widths[name])
                else:
                    # 对于未指定宽度的列，设置一个默认宽度
                    self.table_widget.setColumnWidth(col, 120)
            
            debug("已设置表格初始列宽")
        except Exception as e:
            error(f"设置初始列宽时出错: {str(e)}")
    
    def _setup_connections(self):
        """设置信号连接"""
        # 暂无其他连接
        pass
    
    def _on_item_clicked(self, item):
        """项目点击事件"""
        try:
            # 只处理勾选框列
            if item.column() == 0:
                # 切换勾选框状态
                is_checked = item.checkState() == Qt.CheckState.Checked
                # 设置新状态 - 这里不手动设置，因为用户点击会自动切换
                # item.setCheckState(Qt.CheckState.Unchecked if is_checked else Qt.CheckState.Checked)
                
                # 获取行索引
                row = item.row()
                
                # 更新选中状态 - 这里使用当前状态，不是反转，因为系统已经自动切换了状态
                self._update_row_selection(row, item.checkState() == Qt.CheckState.Checked)
                
                # 记录调试信息
                debug(f"点击勾选框: 行={row}, 状态={item.checkState()}")
        except Exception as e:
            error(f"处理项目点击时出错: {str(e)}")
    
    def _on_header_checkbox_clicked(self, checked):
        """表头勾选框点击事件"""
        try:
            if checked:
                # 全选：将所有过滤后的数据索引添加到选中集合中
                self._checked_rows = set(range(len(self._filtered_data)))
                debug(f"全选：选中所有 {len(self._filtered_data)} 条记录")
            else:
                # 取消全选：清空选中集合
                self._checked_rows.clear()
                debug(f"取消全选：清空所有选中记录")
            
            # 更新当前页显示的勾选框状态
            page = self.pagination.get_current_page()
            rows_per_page = self.pagination.get_rows_per_page()
            start_index = (page - 1) * rows_per_page
            
            # 更新当前页面上的勾选框状态
            self.table_widget.blockSignals(True)  # 阻止信号，避免触发item_clicked事件
            for row in range(self.table_widget.rowCount()):
                data_index = start_index + row
                item = self.table_widget.item(row, 0)
                if item:
                    item.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)
            self.table_widget.blockSignals(False)  # 恢复信号
            
            # 更新数据管理器中的选中索引
            data_manager.set_selected_indices(list(self._checked_rows))
            
            # 更新选中总价显示
            self._update_price_display()
            
        except Exception as e:
            error(f"处理表头勾选框点击时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())
    
    def _update_row_selection(self, row, checked):
        """更新行选择状态"""
        try:
            # 获取当前页码和每页行数
            page = self.pagination.get_current_page()
            rows_per_page = self.pagination.get_rows_per_page()
            
            # 计算数据中的实际索引
            data_index = (page - 1) * rows_per_page + row
            
            if 0 <= data_index < len(self._filtered_data):
                # 更新已选择行集合
                if checked:
                    self._checked_rows.add(data_index)
                    debug(f"选中行 {row}，数据索引 {data_index}")
                else:
                    self._checked_rows.discard(data_index)
                    debug(f"取消选中行 {row}，数据索引 {data_index}")
                
                # 更新数据管理器中的选中索引
                selected_indices = list(self._checked_rows)
                data_manager.set_selected_indices(selected_indices)
                
                # 更新选中总价显示
                self._update_price_display()
                
                # 更新表头全选框状态
                self._update_header_checkbox_state()
            else:
                error(f"行索引超出范围: 行={row}, 数据索引={data_index}, 过滤数据长度={len(self._filtered_data)}")
                
        except Exception as e:
            error(f"更新行选择状态时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())
    
    def _update_header_checkbox_state(self):
        """更新表头全选框状态"""
        try:
            # 获取当前页数据范围
            page = self.pagination.get_current_page()
            rows_per_page = self.pagination.get_rows_per_page()
            start_index = (page - 1) * rows_per_page
            end_index = min(start_index + rows_per_page, len(self._filtered_data))
            
            # 检查当前页是否所有行都被选中
            current_page_all_checked = True
            if start_index < end_index:  # 确保有数据
                for i in range(start_index, end_index):
                    if i not in self._checked_rows:
                        current_page_all_checked = False
                        break
            else:
                current_page_all_checked = False
                
            # 更新表头勾选框状态
            self.header.isChecked = current_page_all_checked
            self.header.update()
        except Exception as e:
            error(f"更新表头勾选框状态时出错: {str(e)}")
    
    def _on_fetch_content(self):
        """获取内容按钮点击事件"""
        try:
            # 询问用户是否确认获取内容
            reply = QMessageBox.question(
                self,
                "确认操作",
                "此操作将从所有变更单中获取内容，并更新表格数据。继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            # 获取所有数据
            self._current_data = data_manager.get_all_items_data()
            
            # 特殊调试: 检查获取的数据中是否包含特定Excel文件中的"报废"值
            debug("特殊调试: 检查所有数据中是否包含Excel文件中的'报废'值...")
            found_scrap_values = False
            scrap_items = []
            
            for idx, item in enumerate(self._current_data):
                item_code = item.get('零件编码', '')
                in_use_value = item.get('已有零件处理建议-使用中', '')
                
                # 检查是否与我们知道应该有"报废"值的条目匹配
                if item_code in ['20065-04-130', '20065-04-340', '20065-04-205', '20065-04-206']:
                    debug(f"找到目标零件 {item_code}, '已有零件处理建议-使用中'值 = '{in_use_value}'")
                    scrap_items.append((item_code, in_use_value))
                    if in_use_value == '报废':
                        found_scrap_values = True
            
            debug(f"找到的目标零件总数: {len(scrap_items)}")
            debug(f"包含'报废'值的目标零件: {found_scrap_values}")
            for code, value in scrap_items:
                debug(f"  零件 {code}: 使用中值 = '{value}'")
                
            # 同时更新过滤后的数据
            self._filtered_data = self._current_data.copy()
            
            # 清空选中行
            self._checked_rows.clear()
            
            # 刷新表格显示
            self._refresh_table()
            
            # 更新状态标签
            self.status_label.setText(f"共 {len(self._filtered_data)} 条零件记录")
            
            info(f"成功获取变更单内容，共 {len(self._filtered_data)} 条记录")
        
        except Exception as e:
            error(f"获取变更单内容时出错: {str(e)}")
            QMessageBox.critical(
                self,
                "错误",
                f"获取变更单内容时出错: {str(e)}"
            )
    
    def _on_filter_changed(self, column, filter_values):
        """筛选条件变化事件"""
        try:
            debug(f"过滤条件变化: 列={column} ({self._column_names[column]}), 值={filter_values}，类型={type(filter_values)}")
            
            # 获取所有筛选条件
            filters = {}
            for col_index in range(1, self.table_widget.columnCount()):  # 跳过勾选框列
                # get_filter 返回该列已激活的筛选值列表，或者在没有筛选时返回 None
                # 空列表 [] 是一个有效筛选，表示不选择任何内容
                # 包含空字符串 [''] 的列表也是有效筛选
                col_filter = self.header.get_filter(col_index)
                if col_filter is not None:
                    column_name = self._column_names[col_index]
                    filters[column_name] = col_filter
            
            debug(f"应用筛选条件: 共 {len(filters)} 个条件：{filters}")
            
            # 统一调用 data_manager.get_filtered_items_data 来更新 DataManager 内部的筛选状态
            # 并获取筛选后的数据。
            # 即使 filters 为空 (表示无筛选)，DataManager 也会正确处理并返回所有数据。
            debug(f"ECNContentPage._on_filter_changed: 最终传递给 DataManager 的筛选条件: {filters}")
            self._filtered_data = data_manager.get_filtered_items_data(filters)
            
            if not filters:
                debug("ECNContentPage._on_filter_changed: DataManager 应用了空筛选 (无筛选)，_filtered_data 应包含所有项")
            else:
                debug(f"ECNContentPage._on_filter_changed: DataManager 应用了筛选条件: {list(filters.keys())}")

            # 更新选中行的索引集合，只保留过滤后还存在的行
            old_checked_rows = self._checked_rows.copy()
            self._checked_rows.clear()
            
            # 由于过滤后的数据可能与原始数据不同，需要重新映射选中索引
            # 这里使用零件编码和变更单号来确定匹配项
            for filtered_idx, filtered_item in enumerate(self._filtered_data):
                for old_idx in old_checked_rows:
                    if old_idx < len(self._current_data):
                        old_item = self._current_data[old_idx]
                        if (filtered_item.get("零件编码") == old_item.get("零件编码") and 
                            filtered_item.get("所属变更单单号") == old_item.get("所属变更单单号")):
                            self._checked_rows.add(filtered_idx)
                            break
            
            # 更新数据管理器中的选中索引
            data_manager.set_selected_indices(list(self._checked_rows))
            
            # 重置分页
            self.pagination.reset()
            
            # 刷新表格显示
            self._refresh_table()
            
            # 更新状态标签
            if len(self._filtered_data) == len(self._current_data):
                self.status_label.setText(f"共 {len(self._filtered_data)} 条零件记录")
                debug("显示所有记录，未应用筛选")
            else:
                self.status_label.setText(f"筛选后 {len(self._filtered_data)} 条零件记录 (共 {len(self._current_data)} 条)")
                debug(f"应用筛选后: {len(self._filtered_data)}/{len(self._current_data)} 条记录")
            
            # 更新表头全选框状态
            self._update_header_checkbox_state()
            
            debug(f"应用筛选完成，记录数: {len(self._filtered_data)}，选中行数: {len(self._checked_rows)}")
            
        except Exception as e:
            error(f"应用筛选时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())
    
    def _on_cell_changed(self, row, column):
        """单元格内容变化事件"""
        try:
            # 检查是否是单价列
            if self._column_names[column] == "单价":
                # 获取当前页码和每页行数
                page = self.pagination.get_current_page()
                rows_per_page = self.pagination.get_rows_per_page()
                
                # 计算数据中的实际索引
                data_index = (page - 1) * rows_per_page + row
                
                if 0 <= data_index < len(self._filtered_data):
                    # 获取单价
                    unit_price = self.table_widget.item(row, column).text()
                    
                    # 更新数据管理器中的单价
                    data_manager.update_item_unit_price(data_index, unit_price)
                    
                    # 更新本地数据中的单价和金额
                    quantity = self._filtered_data[data_index].get("数量", "0")
                    amount = calculator.calculate_amount(quantity, unit_price)
                    self._filtered_data[data_index]["单价"] = unit_price
                    self._filtered_data[data_index]["金额"] = amount
                    
                    # 更新金额列显示
                    amount_col = self._column_names.index("金额")
                    self.table_widget.blockSignals(True)  # 阻止信号触发
                    self.table_widget.item(row, amount_col).setText(amount)
                    self.table_widget.blockSignals(False)  # 恢复信号
                    
                    # 更新总价显示
                    self._update_price_display()
                    
                    debug(f"更新单价: 行={row}, 单价={unit_price}, 金额={amount}")
            
        except Exception as e:
            error(f"处理单元格变化时出错: {str(e)}")
    
    def _on_selection_changed(self):
        """表格选择变化事件"""
        # 注意：由于我们现在使用勾选框管理选择，此函数可以不执行任何操作
        # 保留此函数是为了兼容性，但内部逻辑已转移到勾选框相关处理
        pass
    
    def _on_page_changed(self, page):
        """页码变化事件"""
        # 加载指定页数据并显示
        self._load_page_data(page)
        
        # 更新表头全选框状态
        self._update_header_checkbox_state()
    
    def _update_price_display(self):
        """更新总价和选中总价的显示"""
        # 如果不显示价格信息，直接返回
        if not config.SHOW_PRICE_INFO:
            return

        debug(f"ECNContentPage._update_price_display: CALLED. len(self._filtered_data)={len(self._filtered_data)}")
        try:
            # 优化：直接使用 self._filtered_data 计算总价，无需重新查询
            total_price = calculator.calculate_total_price(self._filtered_data)

            # 计算选中行的总价
            selected_price = "0.00"
            checked_items = []

            # 获取所有已勾选项目的数据
            for idx in self._checked_rows:
                if idx < len(self._filtered_data):
                    checked_items.append(self._filtered_data[idx])

            # 计算已勾选项的总价
            if checked_items:
                selected_price = calculator.calculate_total_price(checked_items)

            debug(f"ECNContentPage._update_price_display: Calculated total_price = {total_price}")
            debug(f"ECNContentPage._update_price_display: Calculated selected_price = {selected_price}")

            # 更新显示 (确保控件存在)
            if self.total_price_widget:
                self.total_price_widget.set_price(total_price)
            if self.selected_price_widget:
                self.selected_price_widget.set_price(selected_price)

        except Exception as e:
            error(f"更新价格显示时出错: {e}")
        debug("ECNContentPage._update_price_display: FINISHED.")
    
    def _load_page_data(self, page):
        """加载指定页的数据"""
        try:
            # 获取每页行数
            rows_per_page = self.pagination.get_rows_per_page()
            
            # 计算起始索引和结束索引
            start_index = (page - 1) * rows_per_page
            end_index = min(start_index + rows_per_page, len(self._filtered_data))
            
            # 清空表格并设置行数
            self.table_widget.blockSignals(True)  # 阻止信号触发
            self.table_widget.setRowCount(0)
            self.table_widget.setRowCount(end_index - start_index)
            
            # 添加字段名映射，处理换行符和原始字段名不一致的问题
            field_mapping = {
                "已有零件处理建议\n-在制在购": "已有零件处理建议-在制在购",
                "已有零件处理建议\n-库存": "已有零件处理建议-库存",
                "已有零件处理建议\n-使用中": "已有零件处理建议-使用中",
                #"已有零件处理建议\n-变更成本": "已有零件处理建议-变更成本"
            }
            
            # 获取列表头
            headers = []
            for col in range(self.table_widget.columnCount()):
                headers.append(self.table_widget.horizontalHeaderItem(col).text())
            debug(f"当前页要加载的数据: 页码={page}, 起始索引={start_index}, 结束索引={end_index-1}")
            
            # 特殊调试：检查表格中的四个目标零件是否显示了"报废"值
            debug("在加载表格数据前检查目标零件:")
            target_items = {}
            for i in range(start_index, end_index):
                try:
                    item_data = self._filtered_data[i]
                    item_code = item_data.get('零件编码', '')
                    if item_code in ['20065-04-130', '20065-04-340', '20065-04-205', '20065-04-206']:
                        in_use_value = item_data.get('已有零件处理建议-使用中', '')
                        debug(f"  要加载到表格的目标零件 {item_code}: 使用中值='{in_use_value}'")
                        target_items[item_code] = {'index': i - start_index, 'value': in_use_value}
                except Exception as e:
                    debug(f"处理目标零件时出错: {str(e)}, 索引={i}")
            
            # 如果当前页表格中至少有一个目标零件，应该弹出提示窗口
            if target_items:
                try:
                    target_info = "\n".join([f"零件 {code}: 值='{info['value']}', 行索引={info['index']}'" for code, info in target_items.items()])
                    debug(f"在当前页找到目标零件:\n{target_info}")
                except Exception as e:
                    debug(f"处理目标零件信息时出错: {str(e)}")
                
            # 输出字段映射信息
            for orig_field, mapped_field in field_mapping.items():
                try:
                    if mapped_field in headers:
                        col_index = headers.index(mapped_field)
                        debug(f"映射列 '{orig_field}' -> UI列 '{mapped_field}' 索引: {col_index}")
                except Exception as e:
                    debug(f"处理字段映射时出错: {str(e)}, 字段={orig_field}")
            
            debug(f"加载第 {page} 页数据，行数: {end_index - start_index}")
            
            # 检查调试：先检查self._checked_rows的类型和内容
            debug(f"self._checked_rows类型: {type(self._checked_rows)}")
            if hasattr(self, '_checked_indices'):
                debug(f"self._checked_indices类型: {type(self._checked_indices)}")
            else:
                debug("self._checked_indices 属性不存在")
                # 如果不存在，初始化为空集合
                self._checked_indices = set()
            
            # 填充表格
            for i in range(start_index, end_index):
                try:
                    item_data = self._filtered_data[i]
                    row = i - start_index
                    
                    # 首先添加勾选框列
                    checkbox_item = QTableWidgetItem()
                    checkbox_item.setFlags(Qt.ItemFlag.ItemIsUserCheckable | 
                                        Qt.ItemFlag.ItemIsEnabled | 
                                        Qt.ItemFlag.ItemIsSelectable)
                    
                    # 检查这一行是否在选中行列表中 - 使用索引检查
                    if i in self._checked_indices:
                        checkbox_item.setCheckState(Qt.CheckState.Checked)
                    else:
                        checkbox_item.setCheckState(Qt.CheckState.Unchecked)
                    
                    self.table_widget.setItem(row, 0, checkbox_item)
                    
                    # 遍历表格列
                    for col in range(1, self.table_widget.columnCount()):
                        header_text = headers[col]

                        # 查找对应的字段名
                        field_name = header_text.replace('\n', '')  # 默认移除换行符
                        
                        # 检查是否是映射字段
                        for orig_field, mapped_field in field_mapping.items():
                            if mapped_field == header_text:
                                field_name = orig_field
                                
                        # 特殊调试：如果是目标零件的"使用中"列，检查是否正确设置了"报废"
                        item_code = item_data.get('零件编码', '')
                        if orig_field == "已有零件处理建议-使用中" and item_code in target_items:
                            debug(f"设置表格单元格 [{row}, {col}] 对应零件 {item_code} 的值: '{item_data.get(field_name, '')}'")
                            break
                        
                        value = item_data.get(field_name, "")
                        
                        # 创建单元格项
                        cell_item = QTableWidgetItem(str(value))
                        
                        # 设置金额和数量列右对齐
                        if field_name in ["金额", "单价", "数量"]:
                            cell_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                        
                        # 设置金额和单价列的格式
                        if field_name in ["金额", "单价"]:
                            try:
                                # 获取值，可能是字符串或者数字
                                value_str = str(value) if value else "0"
                                # 确保是浮点数
                                value_float = float(value_str) if value_str else 0.0
                                # 格式化显示
                                formatted = f"{value_float:.2f}"
                                cell_item.setText(formatted)
                            except Exception as e:
                                error(f"格式化{field_name}时出错: {str(e)}")
                        
                        # 只有单价列可编辑
                        if field_name != "单价":
                            cell_item.setFlags(cell_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                        
                        self.table_widget.setItem(row, col, cell_item)
                except Exception as e:
                    debug(f"处理行 {i} 时出错: {str(e)}")
                    import traceback
                    debug(traceback.format_exc())
            
            # 恢复信号处理
            self.table_widget.blockSignals(False)
            
            # 设置初始列宽
            self._set_initial_column_widths()
            
            # 更新状态标签
            self.status_label.setText(f"共 {len(self._filtered_data)} 条记录")
            
            info(f"刷新表格完成，总行数: {len(self._filtered_data)}")
            
        except Exception as e:
            error(f"加载表格数据时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())
            QMessageBox.critical(
                self,
                "错误",
                f"加载表格数据时出错: {str(e)}"
            )
    
    def _refresh_table(self):
        """刷新表格内容"""
        try:
            # 设置分页控件的总行数
            self.pagination.set_total_rows(len(self._filtered_data))
            
            # 加载当前页数据
            self._load_page_data(self.pagination.get_current_page())
            
            # 确保列宽合理
            self._set_initial_column_widths()
            
            # 更新价格显示
            self._update_price_display()
            
            info(f"刷新表格完成，总行数: {len(self._filtered_data)}")
            
        except Exception as e:
            error(f"刷新表格时出错: {str(e)}")
    
    def refresh(self):
        """供外部调用的刷新方法"""
        self._on_fetch_content()
    
    def _on_clear_filters_clicked(self):
        """取消过滤按钮点击事件"""
        try:
            debug("ECNContentPage: '取消过滤'按钮被点击")
            # 1. 清除 FilterHeader 中的筛选条件
            self.header.clear_filters() # This clears self.header._filters and updates header view
            
            # 2. 更新 DataManager 的筛选条件为空，并获取所有数据
            #   调用 get_filtered_items_data({}) 会将 DataManager 内部的筛选器设置为空
            self._filtered_data = data_manager.get_filtered_items_data({})
            debug(f"ECNContentPage: DataManager 应用了空筛选，_filtered_data 更新为 {len(self._filtered_data)} 条记录")
            
            # 3. 清空选中行状态 (因为筛选变了，之前的选中可能不再有效或意义改变)
            self._checked_rows.clear()
            data_manager.set_selected_indices([]) # 更新 DataManager 中的选中状态
            
            # 4. 重置分页并刷新表格
            self.pagination.reset() # 会触发页面加载和价格更新
            self._refresh_table()  # 确保表格内容和价格显示都更新
            
            # 5. 更新状态标签
            self.status_label.setText(f"共 {len(self._filtered_data)} 条零件记录 (已取消所有筛选)")
            
            # 6. 更新表头全选框状态 (应该变为未选中，因为筛选已清除，选中也清除了)
            if self.header.isChecked: # 如果之前是勾选状态
                self.header.isChecked = False
                self.header.update() # 更新表头UI
            
            info("已取消所有筛选条件")
            
        except Exception as e:
            error(f"取消筛选时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def get_currently_displayed_unique_values(self, column_index: int) -> List[Any]:
        """
        获取当前表格中实际显示的、指定列的唯一值列表。
        这些值是经过所有当前应用的筛选条件过滤后剩下的。
        """
        if not self._filtered_data: # 如果没有数据显示
            return []

        if column_index < 0 or column_index >= len(self._column_names):
            error(f"ECNContentPage.get_currently_displayed_unique_values: 无效的列索引 {column_index}")
            return []
        
        column_name = self._column_names[column_index]
        
        # 添加字段名映射，处理换行符和原始字段名不一致的问题
        field_mapping = {
            "已有零件处理建议\n-在制在购": "已有零件处理建议-在制在购",
            "已有零件处理建议\n-库存": "已有零件处理建议-库存",
            "已有零件处理建议\n-使用中": "已有零件处理建议-使用中",
            #"已有零件处理建议\n-变更成本": "已有零件处理建议-变更成本"
        }
        
        # 检查是否是映射字段，如果是则使用映射后的字段名
        field_name = column_name
        if column_name in field_mapping:
            field_name = field_mapping[column_name]
            debug(f"获取当前唯一值时使用映射字段: {column_name} -> {field_name}")
            
        unique_values = set()
        
        for item_data in self._filtered_data:
            value = item_data.get(field_name)
            if value is not None:
                unique_values.add(value)
            # 添加空字符串统计（在模型中可能是空字符串而不是None）
            elif item_data.get(field_name, "") == "":
                unique_values.add("")
        
        # 转换为列表并排序，尝试字符串排序以保持一致性
        try:
            sorted_list = sorted(list(unique_values), key=lambda x: str(x).lower())
        except Exception as e:
            debug(f"ECNContentPage.get_currently_displayed_unique_values: 排序时出错 {e}, 返回未排序列表")
            sorted_list = list(unique_values)
            
        debug(f"ECNContentPage.get_currently_displayed_unique_values: 列 '{column_name}' ({column_index}) 的唯一显示值: {sorted_list}")
        return sorted_list

    def get_value_statistics_for_column(self, column_index: int) -> Dict[Any, Dict[str, int]]:
        """
        获取指定列中每个唯一值在原始数据和当前显示数据中的出现次数。
        返回一个字典，键是列中的唯一值，值是另一个包含 'total' 和 'displayed' 计数的字典。
        """
        statistics = {}
        if column_index < 0 or column_index >= len(self._column_names):
            error(f"ECNContentPage.get_value_statistics_for_column: 无效的列索引 {column_index}")
            return statistics

        column_name = self._column_names[column_index]
        
        # 添加字段名映射，处理换行符和原始字段名不一致的问题
        field_mapping = {
            "已有零件处理建议\n-在制在购": "已有零件处理建议-在制在购",
            "已有零件处理建议\n-库存": "已有零件处理建议-库存",
            "已有零件处理建议\n-使用中": "已有零件处理建议-使用中",
            #"已有零件处理建议\n-变更成本": "已有零件处理建议-变更成本"
        }
        
        # 检查是否是映射字段，如果是则使用映射后的字段名
        field_name = column_name
        if column_name in field_mapping:
            field_name = field_mapping[column_name]
            debug(f"统计时使用映射字段: {column_name} -> {field_name}")

        # 初始化统计字典，并计算每个值在原始数据中的总出现次数
        for item_data in self._current_data:
            value = item_data.get(field_name)
            if value is not None:
                if value not in statistics:
                    statistics[value] = {"total": 0, "displayed": 0}
                statistics[value]["total"] += 1
        
        # 计算每个值在当前显示数据中的出现次数
        if self._filtered_data:
            for item_data in self._filtered_data:
                value = item_data.get(field_name)
                if value is not None and value in statistics: # 值必须已在原始数据中存在
                    statistics[value]["displayed"] += 1
        
        # 添加空字符串统计（在模型中可能是空字符串而不是None）
        for item_data in self._current_data:
            value = item_data.get(field_name, "")
            if value == "":
                if "" not in statistics:
                    statistics[""] = {"total": 0, "displayed": 0}
                statistics[""]["total"] += 1
                
        if self._filtered_data:
            for item_data in self._filtered_data:
                value = item_data.get(field_name, "")
                if value == "" and "" in statistics:
                    statistics[""]["displayed"] += 1
        
        debug(f"ECNContentPage.get_value_statistics_for_column: 列 '{column_name}' ({column_index}) 的统计数据: {statistics}")
        return statistics

    # 添加一个新方法，用于获取指定列所有可能的值（不受筛选影响）
    def get_all_column_values(self, column_index):
        """
        获取指定列所有可能的值，不受当前筛选条件的影响
        
        Args:
            column_index: 列索引
            
        Returns:
            包含所有可能值的列表
        """
        values = []
        column_name = self._column_names[column_index]
        
        # 添加字段名映射，处理换行符和原始字段名不一致的问题
        field_mapping = {
            "已有零件处理建议\n-在制在购": "已有零件处理建议-在制在购",
            "已有零件处理建议\n-库存": "已有零件处理建议-库存",
            "已有零件处理建议\n-使用中": "已有零件处理建议-使用中",
            #"已有零件处理建议\n-变更成本": "已有零件处理建议-变更成本"
        }
        
        # 检查是否是映射字段，如果是则使用映射后的字段名
        field_name = column_name
        if column_name in field_mapping:
            field_name = field_mapping[column_name]
            debug(f"获取所有列值时使用映射字段: {column_name} -> {field_name}")
        
        # 从所有原始数据中获取该列的唯一值
        unique_values = set()
        for item in self._current_data:
            value = item.get(field_name)
            # 将 None 值视为空字符串，以保持筛选的一致性
            if value is None:
                value = ""
            unique_values.add(value)
                
        # 对值进行排序并返回
        values = sorted(list(unique_values), key=lambda x: str(x).lower())
        return values

    def _on_fetch_prices_clicked(self):
        """获取零件单价按钮点击事件"""
        # 如果不显示价格信息，直接返回
        if not config.SHOW_PRICE_INFO:
            return

        if not data_manager.ecn_items:
            QMessageBox.information(self, "提示", "请先获取变更内容。")
            return
            
        reply = QMessageBox.question(
            self, "确认操作", "此操作将从数据库获取单价，并可能覆盖您手动修改的值。\n确定要继续吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return

        # 创建并配置进度条对话框
        self.progress_dialog = QProgressDialog("正在从数据库获取单价...", "取消", 0, len(data_manager.ecn_items), self)
        self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        self.progress_dialog.setWindowTitle("处理中")
        self.progress_dialog.setAutoClose(False)
        self.progress_dialog.setAutoReset(True)
            
        try:
            # 创建一个临时的DB管理器实例仅用于连接测试
            if not DatabaseManager().connect():
                QMessageBox.critical(self, "数据库连接失败", "无法连接到数据库，请检查配置和网络。")
                return

            # 创建后台工作线程
            worker = Worker(data_manager.fetch_prices_in_background)
            
            # 连接信号
            worker.signals.progress.connect(self.progress_dialog.setValue)
            worker.signals.finished.connect(self._on_fetching_prices_finished)
            worker.signals.error.connect(self._on_fetching_prices_error)
            
            # 当用户点击取消时
            self.progress_dialog.canceled.connect(lambda: self.thread_pool.clear()) # 待优化

            # 开始执行
            self.thread_pool.start(worker)
            self.progress_dialog.show()

        except Exception as e:
            error(f"启动获取单价任务时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动任务时出错: {str(e)}")

    def _on_fetching_prices_finished(self, updated_count: int):
        """后台获取单价任务完成后的处理"""
        info(f"用户触发的单价更新完成，共更新 {updated_count} 条记录")
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        # 修复：获取当前筛选条件（从控件值中构建）或使用空字典
        filters = {}
        # 如果目前有应用筛选条件，从界面控件获取它们
        if hasattr(self, 'filter_fields') and self.filter_fields:
            for field_name, filter_widget in self.filter_fields.items():
                if isinstance(filter_widget, QLineEdit) and filter_widget.text().strip():
                    filters[field_name] = filter_widget.text().strip()
                elif isinstance(filter_widget, QComboBox) and filter_widget.currentText() != "全部":
                    filters[field_name] = filter_widget.currentText()

        # 重新从data_manager获取带有最新单价的全量数据，并应用当前筛选器
        self._filtered_data = data_manager.get_filtered_items_data(filters)
        
        # 刷新表格
        self._refresh_table()

        QMessageBox.information(self, "更新完成", f"成功更新 {updated_count} 个零件的单价。")

    def _on_fetching_prices_error(self, error_message: str):
        """通用任务出错处理"""
        if hasattr(self, 'progress_dialog') and self.progress_dialog.isVisible():
            self.progress_dialog.close()
        QMessageBox.critical(self, "任务失败", f"处理过程中发生错误:\n{error_message}")
        
    def _on_export_excel_clicked(self):
        """导出Excel按钮点击事件"""
        try:
            debug("ECNContentPage: '导出Excel'按钮被点击")
            
            # 检查是否有数据可导出
            if not self._filtered_data:
                QMessageBox.information(self, "导出提示", "没有数据可导出。")
                return
            
            # 获取选中的行，这次使用DataManager中记录的所有选中行索引，而不仅仅是当前页面显示的行
            selected_indices = list(self._checked_rows)
            debug(f"导出Excel: 总共选中了 {len(selected_indices)} 行")
            
            # 如果没有选中行，询问是否导出所有行
            if not selected_indices:
                reply = QMessageBox.question(
                    self, "导出确认", 
                    "当前没有选中任何行，是否要导出所有数据？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    return
                    
                # 导出所有数据
                data_to_export = self._filtered_data.copy()
                info(f"导出Excel: 导出所有 {len(data_to_export)} 行数据")
            else:
                # 导出选中行
                # 确保索引不超出范围
                valid_indices = [i for i in selected_indices if i < len(self._filtered_data)]
                data_to_export = [self._filtered_data[i] for i in valid_indices]
                info(f"导出Excel: 从 {len(selected_indices)} 个选中行中导出 {len(data_to_export)} 行有效数据")
            
            # 选择保存路径
            suggested_filename = f"变更单内容_{len(data_to_export)}行.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存Excel文件", suggested_filename, 
                "Excel文件 (*.xlsx);;All Files (*)"
            )
            
            if not file_path:
                return  # 用户取消了保存
                
            # 如果文件名没有.xlsx后缀，自动添加
            if not file_path.lower().endswith('.xlsx'):
                file_path += '.xlsx'
            
            # 添加字段名映射，处理换行符和原始字段名不一致的问题
            field_mapping = {
                "已有零件处理建议\n-在制在购": "已有零件处理建议-在制在购",
                "已有零件处理建议\n-库存": "已有零件处理建议-库存",
                "已有零件处理建议\n-使用中": "已有零件处理建议-使用中",
                #"已有零件处理建议\n-变更成本": "已有零件处理建议-变更成本"
            }
            # 反向映射，将数据模型字段名映射回显示名称
            reverse_mapping = {v: k for k, v in field_mapping.items()}
            
            # 将数据转换为DataFrame，需要处理字段名映射
            visible_columns = self._column_names[1:]  # 排除第一个"全选"列
            
            # 预处理数据，确保映射字段能正确匹配
            processed_data = []
            for item in data_to_export:
                processed_item = {}
                for col in visible_columns:
                    if col in field_mapping:
                        # 在数据模型中查找映射后的字段名
                        model_field_name = field_mapping[col]
                        processed_item[col] = item.get(model_field_name, "")
                    else:
                        # 正常字段直接获取
                        processed_item[col] = item.get(col, "")
                processed_data.append(processed_item)
            
            # 创建DataFrame
            df_visible = pd.DataFrame(processed_data)
            
            # 导出到Excel
            df_visible.to_excel(file_path, index=False, engine='openpyxl')
            
            # 通知用户导出成功
            QMessageBox.information(
                self, "导出成功", 
                f"成功导出 {len(data_to_export)} 行数据到:\n{file_path}"
            )
            info(f"已成功导出数据到Excel: {file_path}")
            
        except Exception as e:
            error_msg = f"导出Excel时出错: {str(e)}"
            error(error_msg)
            import traceback
            error(traceback.format_exc())
            QMessageBox.critical(self, "导出错误", error_msg) 